# 📦 Emma Studio Product Placement

Una herramienta avanzada de colocación de productos que permite crear mockups profesionales para marketing utilizando inteligencia artificial.

## 🌟 Características Principales

### ✨ Generación de Fondos con IA
- **Ideogram AI Integration**: Utiliza Ideogram 3.0 para generar fondos realistas
- **Estilos Múltiples**: Realista, Minimalista, Lifestyle, Comercial
- **Escenas Predefinidas**: Cocina moderna, plaza exterior, cafetería, oficina, etc.
- **Prompts Personalizados**: Describe tu escena ideal en lenguaje natural

### 🖼️ Procesamiento de Productos
- **Formatos Soportados**: PNG, JPEG, WEBP
- **Optimización Automática**: Redimensionado y mejora de calidad
- **Soporte de Transparencia**: Mantiene canales alfa para composición perfecta
- **Validación de Archivos**: Verificación de formato y tamaño

### 🎨 Composición Avanzada
- **Posicionamiento Preciso**: Controles X/Y con precisión decimal
- **Escalado Dinámico**: 10% - 200% del tamaño original
- **Rotación Completa**: -180° a +180° con pasos de 5°
- **Sombras Realistas**: Intensidad ajustable 0-100%

### 🎯 Interfaz de Usuario
- **Diseño Emma Studio**: Colores de marca (#3018ef, #dd3a5a)
- **Efectos Glassmorphism**: Transparencias y desenfoque de fondo
- **Interfaz en Español**: Completamente localizada
- **Responsive Design**: Funciona en desktop y móvil

## 🏗️ Arquitectura Técnica

### Backend (Python/FastAPI)
```
backend/
├── app/services/product_placement_service.py  # Servicio principal
├── app/api/endpoints/product_placement.py     # Endpoints API
└── test_product_placement.py                  # Tests
```

### Frontend (React/TypeScript)
```
client/src/
├── pages/product-placement-page.tsx           # Página principal
├── services/product-placement-service.ts     # Cliente API
└── data/ai-tools-data.ts                     # Configuración herramienta
```

## 🚀 Instalación y Configuración

### 1. Configurar Variables de Entorno
```bash
# Backend (.env)
IDEOGRAM_API_KEY=tu_api_key_aqui
```

### 2. Instalar Dependencias Backend
```bash
cd backend
pip install pillow httpx fastapi
```

### 3. Instalar Dependencias Frontend
```bash
cd client
npm install
```

### 4. Ejecutar Tests
```bash
cd backend
python test_product_placement.py
```

## 📡 API Endpoints

### Generar Fondo
```http
POST /api/product-placement/generate-background
Content-Type: application/json

{
  "scene_prompt": "modern kitchen with marble countertops",
  "style": "realistic",
  "size": "1024x1024"
}
```

### Subir Producto
```http
POST /api/product-placement/upload-product
Content-Type: multipart/form-data

product_image: [archivo]
```

### Crear Composición
```http
POST /api/product-placement/composite
Content-Type: application/json

{
  "background_url": "data:image/png;base64,...",
  "product_image_data": "data:image/png;base64,...",
  "position": {"x": 0.5, "y": 0.5},
  "scale": 1.0,
  "rotation": 0.0,
  "shadow_intensity": 0.3
}
```

### Obtener Sugerencias
```http
GET /api/product-placement/scene-suggestions
GET /api/product-placement/styles
```

## 🎯 Flujo de Trabajo

1. **Subir Producto**: El usuario sube una imagen de su producto
2. **Seleccionar Escena**: Elige una escena predefinida o describe una personalizada
3. **Generar Fondo**: Ideogram AI genera el fondo basado en el prompt
4. **Posicionar Producto**: Ajusta posición, escala, rotación y sombra
5. **Crear Composición**: El sistema combina producto y fondo
6. **Descargar Resultado**: Imagen final en alta calidad

## 🚀 Cómo Usar

### Acceso a la Herramienta
1. **Iniciar servidores**:
   ```bash
   # Backend
   uvicorn app.main:app --reload

   # Frontend
   npm run dev
   ```

2. **Navegar a Product Placement**:
   - Ve a **Ads Central** en el sidebar
   - Haz clic en **📦 Product Placement**
   - O navega directamente a: `/dashboard/ads-central/product-placement`

### Flujo Completo
1. **Upload producto** → **Generar fondo** → **Posicionar** → **Descargar**

## 🔧 Configuración Avanzada

### Estilos de Fondo Disponibles
- **Realista**: Fotografía profesional con iluminación natural
- **Minimalista**: Fondo limpio con colores neutros
- **Lifestyle**: Ambiente natural y auténtico
- **Comercial**: Configuración de estudio profesional

### Escenas Predefinidas
- Cocina Moderna
- Plaza Exterior  
- Cafetería
- Escritorio de Oficina
- Sala de Estar
- Tienda Retail
- Parque Natural
- Estudio Fotográfico

## 🐛 Solución de Problemas

### Error: "Ideogram API key not configured"
- Verificar que `IDEOGRAM_API_KEY` esté configurada
- Reiniciar el servidor backend

### Error: "Formato de imagen no soportado"
- Usar solo PNG, JPEG o WEBP
- Verificar que el archivo no esté corrupto

### Error: "No se pudo generar el fondo"
- Verificar conexión a internet
- Comprobar que la API key de Ideogram sea válida
- Intentar con un prompt más simple

## 📈 Métricas y Monitoreo

### Logs del Sistema
- Generación de fondos: `🎨 Generating background scene`
- Procesamiento de productos: `📦 Processing product image`
- Composición: `🖼️ Compositing product on background`

### Health Check
```http
GET /api/product-placement/health
```

## 🔮 Próximas Funcionalidades

- [ ] Múltiples productos en una escena
- [ ] Plantillas de composición predefinidas
- [ ] Efectos de iluminación avanzados
- [ ] Integración con redes sociales
- [ ] Batch processing de productos
- [ ] Análisis de rendimiento de mockups

## 🤝 Contribución

Para contribuir al desarrollo:

1. Fork el repositorio
2. Crear una rama feature: `git checkout -b feature/nueva-funcionalidad`
3. Commit cambios: `git commit -am 'Agregar nueva funcionalidad'`
4. Push a la rama: `git push origin feature/nueva-funcionalidad`
5. Crear Pull Request

## 📄 Licencia

Este proyecto es parte de Emma Studio y está sujeto a los términos de licencia del proyecto principal.

---

**Desarrollado con ❤️ por el equipo de Emma Studio**
