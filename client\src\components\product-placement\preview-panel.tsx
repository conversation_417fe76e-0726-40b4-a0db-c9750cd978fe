import React from "react";
import { ImageIcon, Download } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface PreviewPanelProps {
  compositeImage: string | null;
  backgroundImage: string | null;
  onDownload: () => void;
}

export function PreviewPanel({ 
  compositeImage, 
  backgroundImage, 
  onDownload 
}: PreviewPanelProps) {
  return (
    <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl h-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-slate-800">
            <ImageIcon className="w-5 h-5 text-[#3018ef]" />
            Vista Previa
          </CardTitle>
          {compositeImage && (
            <Button
              variant="outline"
              size="sm"
              onClick={onDownload}
              className="hover:bg-[#3018ef]/5 hover:border-[#3018ef]"
            >
              <Download className="w-4 h-4 mr-2" />
              Descargar
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="h-[600px] flex items-center justify-center">
        {compositeImage ? (
          <div className="relative max-w-full max-h-full">
            <img
              src={compositeImage}
              alt="Composición final"
              className="max-w-full max-h-full object-contain rounded-lg shadow-lg"
            />
          </div>
        ) : backgroundImage ? (
          <div className="relative max-w-full max-h-full">
            <img
              src={backgroundImage}
              alt="Fondo generado"
              className="max-w-full max-h-full object-contain rounded-lg shadow-lg opacity-80"
            />
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="bg-white/90 backdrop-blur-sm rounded-lg p-4 text-center">
                <p className="text-slate-600 font-medium">Fondo generado</p>
                <p className="text-sm text-slate-500">Ajusta la posición y crea la composición</p>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center text-slate-500">
            <ImageIcon className="w-16 h-16 mx-auto mb-4 text-slate-300" />
            <p className="text-lg font-medium">Vista Previa</p>
            <p className="text-sm">Sube un producto y genera un fondo para ver la composición</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
