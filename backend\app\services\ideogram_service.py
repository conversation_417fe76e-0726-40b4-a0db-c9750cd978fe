"""
Ideogram.ai Service - Enhanced with two-layer system support
Generates clean backgrounds for text overlay + traditional text-embedded images
Perfect for professional marketing materials with superior text quality control.
"""

import logging
import httpx
import os
from typing import Dict, Any
from fastapi import UploadFile
from app.core.config import settings

logger = logging.getLogger(__name__)


class IdeogramService:
    """Service for generating advertisements using Ideogram.ai - optimized for text and marketing."""
    
    def __init__(self):
        """Initialize the Ideogram service."""
        self.api_key = os.getenv("IDEOGRAM_API_KEY") or settings.IDEOGRAM_API_KEY
        self.base_url = "https://api.ideogram.ai/v1"
        self.v3_endpoint = "https://api.ideogram.ai/v1/ideogram-v3/generate"  # New Ideogram 3.0 endpoint
        
    async def generate_image(self, prompt: str, dimensions: Dict[str, int] = None) -> Dict[str, Any]:
        """
        Generate an image using Ideogram.ai - main method for post generation.
        LEGACY METHOD - Still supports text-embedded images for backward compatibility.

        Args:
            prompt: Description of the image to create
            dimensions: Dictionary with width and height (e.g., {"width": 1080, "height": 1080})

        Returns:
            Dict with success status, image data, and metadata
        """
        # Convert dimensions to size string
        if dimensions:
            width = dimensions.get("width", 1024)
            height = dimensions.get("height", 1024)
            size = f"{width}x{height}"
        else:
            size = "1024x1024"

        # Use the existing generate_ad method
        return await self.generate_ad(prompt, size)

    async def generate_photography(self, prompt: str, size: str = "1024x1024", num_images: int = 4) -> Dict[str, Any]:
        """
        Generate photographic images using Ideogram.ai optimized for camera-like realism.
        Uses magic prompt and realistic style for authentic photography results.

        Args:
            prompt: Description of the photograph to create
            size: Image size (1024x1024, 1024x1792, 1792x1024)
            num_images: Number of photographic variations to generate

        Returns:
            Dict with success status, image data, and metadata
        """
        if not self.api_key:
            logger.error("Ideogram API key not configured")
            return {"success": False, "error": "Ideogram API key not configured"}

        try:
            # Enhanced photographic prompt with camera realism - AVOID TEXT EMBEDDING
            photographic_prompt = f"""A professional photograph showing {prompt}.

High-quality photography captured with professional camera equipment. Natural lighting, authentic photographic composition, realistic depth of field, professional photographer quality, high resolution, sharp focus, natural colors, photorealistic, camera-captured image.

IMPORTANT: This should be a clean photograph without any text, words, or letters visible in the image. Pure photographic content only."""

            # Map common sizes to Ideogram format
            size_mapping = {
                "1024x1024": "1024x1024",
                "1024x1792": "1024x1792",
                "1792x1024": "1792x1024",
                "auto": "1024x1024"
            }

            ideogram_size = size_mapping.get(size, "1024x1024")

            # Optimized settings for photographic realism
            files = {
                "prompt": (None, photographic_prompt),
                "resolution": (None, ideogram_size),
                "rendering_speed": (None, "QUALITY"),  # Highest quality for photography
                "magic_prompt": (None, "ON"),  # ENABLE magic prompt for photographic enhancement
                "style_type": (None, "REALISTIC"),  # Use REALISTIC style for camera-like images
                "negative_prompt": (None, "text, words, letters, writing, typography, signs, labels, watermarks, digital art, illustration, cartoon, anime, painting, drawing, sketch, CGI, 3D render, artificial, synthetic, fake, unrealistic, low quality, blurry, pixelated, amateur"),
                "num_images": (None, str(num_images))
            }

            headers = {
                "Api-Key": self.api_key
            }

            logger.info(f"📸 Generating {num_images} photographic images with Ideogram.ai: {prompt[:100]}...")

            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    self.v3_endpoint,  # Use v3 endpoint for photography
                    files=files,
                    headers=headers
                )

                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"Ideogram photography error {response.status_code}: {error_text}")
                    return {"success": False, "error": f"Ideogram error: {error_text}"}

                result = response.json()

                # Process multiple photographic images
                if "data" in result and len(result["data"]) > 0:
                    images = []
                    logger.info(f"📊 Processing {len(result['data'])} photographic images from Ideogram")

                    for i, image_data in enumerate(result["data"]):
                        image_url = image_data.get("url")
                        if image_url:
                            logger.info(f"✅ Photography {i+1}: {image_url[:50]}...")
                            images.append({
                                "image_url": image_url,
                                "revised_prompt": image_data.get("prompt"),
                                "metadata": {
                                    "model": "ideogram-3.0-realistic",
                                    "size": size,
                                    "resolution": ideogram_size,
                                    "original_prompt": prompt,
                                    "photographic_prompt": photographic_prompt,
                                    "seed": image_data.get("seed"),
                                    "variation": i + 1,
                                    "is_image_safe": image_data.get("is_image_safe", True),
                                    "style_type": "REALISTIC",
                                    "magic_prompt": "ON",
                                    "photography_optimized": True
                                }
                            })

                    if images:
                        # Extract URLs for compatibility
                        image_urls = [img["image_url"] for img in images]
                        logger.info(f"📸 Successfully generated {len(images)} photographic images")

                        return {
                            "success": True,
                            "image_url": image_urls[0],  # First image as primary
                            "all_images": image_urls,
                            "images": image_urls,  # For frontend compatibility
                            "num_generated": len(images),
                            "revised_prompt": images[0]["revised_prompt"],
                            "metadata": {
                                "model": "ideogram-3.0-realistic",
                                "size": size,
                                "resolution": ideogram_size,
                                "original_prompt": prompt,
                                "photographic_prompt": photographic_prompt,
                                "num_requested": num_images,
                                "num_generated": len(images),
                                "type": "photography_generation",
                                "rendering_speed": "QUALITY",
                                "magic_prompt": "ON",
                                "style_type": "REALISTIC",
                                "photography_optimized": True,
                                "all_images_data": images
                            }
                        }
                    else:
                        logger.error("❌ No valid photographic images found in response")
                        return {"success": False, "error": "No valid images in response"}
                else:
                    logger.error(f"❌ Invalid photography response structure: {result}")
                    return {"success": False, "error": "No image data in response"}

        except Exception as e:
            logger.error(f"Error generating photography with Ideogram: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}

    async def generate_clean_background(self, prompt: str, dimensions: Dict[str, int] = None) -> Dict[str, Any]:
        """
        Generate a clean background image WITHOUT text for two-layer system.

        This method creates background images optimized for text overlay,
        ensuring better text quality and positioning control.

        Args:
            prompt: Description of the clean background to create (should not include text)
            dimensions: Dictionary with width and height (e.g., {"width": 1080, "height": 1080})

        Returns:
            Dict with success status, image data, and metadata
        """
        # Convert dimensions to size string
        if dimensions:
            width = dimensions.get("width", 1024)
            height = dimensions.get("height", 1024)
            size = f"{width}x{height}"
        else:
            size = "1024x1024"

        # Enhanced negative prompt to ensure NO text appears
        enhanced_negative_prompt = (
            "text, letters, words, typography, font, writing, captions, titles, labels, "
            "signs, banners, watermarks, logos with text, readable text, any text elements, "
            "blurry, low quality, pixelated, amateur, unprofessional, poor lighting, "
            "bad composition, unclear, fuzzy, messy, distorted, poor quality"
        )

        return await self._generate_with_enhanced_settings(
            prompt=prompt,
            size=size,
            negative_prompt=enhanced_negative_prompt,
            generation_type="clean_background"
        )

    async def _generate_with_enhanced_settings(self, prompt: str, size: str, negative_prompt: str, generation_type: str) -> Dict[str, Any]:
        """
        Internal method for generating images with enhanced settings for two-layer system.

        Args:
            prompt: Image description
            size: Image size
            negative_prompt: Enhanced negative prompt
            generation_type: Type of generation (clean_background, etc.)

        Returns:
            Dict with success status, image data, and metadata
        """
        if not self.api_key:
            logger.error("Ideogram API key not configured")
            return {"success": False, "error": "Ideogram API key not configured"}

        try:
            # Map common sizes to Ideogram format
            size_mapping = {
                "1024x1024": "1024x1024",
                "1024x1792": "1024x1792",
                "1792x1024": "1792x1024",
                "auto": "1024x1024"
            }

            ideogram_size = size_mapping.get(size, "1024x1024")

            logger.info(f"🎨 Generating {generation_type} with Ideogram.ai: {prompt[:100]}...")

            async with httpx.AsyncClient(timeout=120.0) as client:
                # Use Ideogram 3.0 with correct format

                # Convert size string to correct aspect ratio format
                if size and "x" in size:
                    width_str, height_str = size.split("x")
                    width = int(width_str)
                    height = int(height_str)
                    if width == height:
                        aspect_ratio = "1x1"
                    elif width > height:
                        if width / height >= 1.7:
                            aspect_ratio = "16x9"
                        else:
                            aspect_ratio = "3x2"
                    else:
                        if height / width >= 1.7:
                            aspect_ratio = "9x16"
                        else:
                            aspect_ratio = "2x3"
                else:
                    aspect_ratio = "1x1"

                # Use same successful configuration as Photography Generator
                files = {
                    "prompt": (None, prompt),
                    "aspect_ratio": (None, aspect_ratio),
                    "rendering_speed": (None, "QUALITY"),  # Highest quality
                    "magic_prompt": (None, "ON"),  # Enable magic prompt like Photography
                    "style_type": (None, "REALISTIC"),  # Use REALISTIC like Photography
                    "negative_prompt": (None, negative_prompt if negative_prompt else "text, words, letters, watermark, logo, branding"),
                    "num_images": (None, "4")  # Generate 4 options by default
                }

                headers_v3 = {
                    "Api-Key": self.api_key
                    # No Content-Type for multipart/form-data
                }

                logger.info(f"🎨 Using Ideogram 3.0 endpoint: {self.v3_endpoint}")
                logger.info(f"📐 Aspect ratio: {aspect_ratio}")
                logger.info(f"🖼️ Generating 4 images for selection")
                logger.info(f"📋 Using Photography-style configuration")

                response = await client.post(
                    self.v3_endpoint,
                    files=files,
                    headers=headers_v3
                )

                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"Ideogram error {response.status_code}: {error_text}")
                    return {"success": False, "error": f"Ideogram error: {error_text}"}

                result = response.json()

                # Ideogram 3.0 response format - handle multiple images
                if "data" in result and len(result["data"]) > 0:
                    images = []
                    import base64

                    logger.info(f"📊 Processing {len(result['data'])} images from Ideogram response")

                    for idx, image_data in enumerate(result["data"]):
                        image_url = image_data.get("url")
                        if image_url:
                            # Download and convert each image to base64
                            try:
                                async with httpx.AsyncClient() as download_client:
                                    img_response = await download_client.get(image_url)
                                    if img_response.status_code == 200:
                                        image_base64 = base64.b64encode(img_response.content).decode()
                                        image_data_url = f"data:image/png;base64,{image_base64}"

                                        images.append({
                                            "url": image_url,
                                            "data": image_data_url,
                                            "seed": image_data.get("seed"),
                                            "prompt": image_data.get("prompt"),
                                            "is_image_safe": image_data.get("is_image_safe", True),
                                            "style_type": image_data.get("style_type", "GENERAL")
                                        })
                                        logger.info(f"✅ Processed image {idx + 1}/{len(result['data'])}")
                                    else:
                                        logger.warning(f"Failed to download image {idx + 1}")
                            except Exception as e:
                                logger.warning(f"Failed to process image {idx + 1}: {e}")

                    if images:
                        # Return the first image as primary, but include all options
                        primary_image = images[0]
                        return {
                            "success": True,
                            "image_url": primary_image["url"],
                            "image_data": primary_image["data"],
                            "images": images,  # All 4 options
                            "revised_prompt": primary_image["prompt"],
                            "metadata": {
                                "model": "ideogram-3.0-quality",
                                "size": size,
                                "resolution": ideogram_size,
                                "original_prompt": prompt,
                                "generation_type": generation_type,
                                "seed": primary_image["seed"],
                                "is_image_safe": primary_image["is_image_safe"],
                                "style_type": primary_image["style_type"],
                                "two_layer_system": generation_type == "clean_background",
                                "total_images": len(images)
                            }
                        }
                    else:
                        logger.error("Failed to process any images")
                        return {"success": False, "error": "Failed to process images"}
                else:
                    logger.error(f"No data array in response: {result}")
                    return {"success": False, "error": "No image data in response"}

        except Exception as e:
            logger.error(f"Error generating {generation_type} with Ideogram: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}

    async def generate_ad(self, prompt: str, size: str = "1024x1024") -> Dict[str, Any]:
        """
        Generate an advertisement using Ideogram.ai.
        
        Args:
            prompt: Description of the advertisement to create
            size: Image size (1024x1024, 1024x1792, 1792x1024)
            
        Returns:
            Dict with success status, image data, and metadata
        """
        if not self.api_key:
            logger.error("Ideogram API key not configured")
            return {"success": False, "error": "Ideogram API key not configured"}
        
        try:
            # Use the prompt directly as Ideogram recommends - don't over-complicate
            enhanced_prompt = prompt

            # Map common sizes to Ideogram format
            size_mapping = {
                "1024x1024": "1024x1024",
                "1024x1792": "1024x1792",
                "1792x1024": "1792x1024",
                "auto": "1024x1024"
            }

            ideogram_size = size_mapping.get(size, "1024x1024")

            # Prepare multipart form data for Ideogram 3.0 Quality API (excellent for text)
            files = {
                "prompt": (None, enhanced_prompt),
                "resolution": (None, ideogram_size),
                "rendering_speed": (None, "QUALITY"),  # Use QUALITY for best text rendering
                "magic_prompt": (None, "ON"),  # Force magic prompt ON for better text enhancement
                "style_type": (None, "DESIGN"),  # Use DESIGN style for better text integration
                "negative_prompt": (None, "blurry text, illegible text, distorted text, unreadable text, poor typography, bad font, unclear letters, pixelated text, low quality text, amateur typography, unprofessional text, watermark, signature, low resolution, blurry, distorted, poor quality, amateur design, bad composition, unclear, fuzzy text, messy text"),
                "num_images": (None, "1")  # Single image for individual calls
            }

            headers = {
                "Api-Key": self.api_key
            }

            logger.info(f"🎨 Generating advertisement with Ideogram.ai: {prompt[:100]}...")

            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    f"{self.base_url}/generate",
                    files=files,
                    headers=headers
                )
                
                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"Ideogram error {response.status_code}: {error_text}")
                    return {"success": False, "error": f"Ideogram error: {error_text}"}
                
                result = response.json()

                # Ideogram 3.0 response format
                if "data" in result and len(result["data"]) > 0:
                    image_data = result["data"][0]
                    image_url = image_data.get("url")

                    if image_url:
                        return {
                            "success": True,
                            "image_url": image_url,
                            "revised_prompt": image_data.get("prompt"),
                            "metadata": {
                                "model": "ideogram-3.0-quality",
                                "size": size,
                                "resolution": ideogram_size,
                                "original_prompt": prompt,
                                "enhanced_prompt": enhanced_prompt,
                                "seed": image_data.get("seed"),
                                "is_image_safe": image_data.get("is_image_safe", True),
                                "style_type": image_data.get("style_type", "GENERAL")
                            }
                        }
                    else:
                        logger.error(f"No image URL found in response: {image_data}")
                        return {"success": False, "error": "No image URL in response"}
                else:
                    logger.error(f"No data array in response: {result}")
                    return {"success": False, "error": "No image data in response"}
                    
        except Exception as e:
            logger.error(f"Error generating advertisement with Ideogram: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}

    async def generate_multiple_ads(self, prompt: str, num_images: int = 3, size: str = "1024x1024") -> Dict[str, Any]:
        """
        Generate multiple advertisements in a single API call using Ideogram.ai.
        Uses best practices: QUALITY rendering, AUTO magic prompt, DESIGN style.

        Args:
            prompt: Description of the advertisement to create
            num_images: Number of images to generate (1-4, default 3)
            size: Image size

        Returns:
            Dict with success status, image URLs list, and metadata
        """
        if not self.api_key:
            logger.error("Ideogram API key not configured")
            return {"success": False, "error": "Ideogram API key not configured"}

        # Limit to API maximum
        num_images = min(max(num_images, 1), 8)

        try:
            # Use the prompt directly as Ideogram recommends - don't over-complicate
            enhanced_prompt = prompt

            # Map common sizes to Ideogram format
            size_mapping = {
                "1024x1024": "1024x1024",
                "1024x1792": "1024x1792",
                "1792x1024": "1792x1024",
                "auto": "1024x1024"
            }

            ideogram_size = size_mapping.get(size, "1024x1024")

            # Prepare multipart form data for multiple images with enhanced text quality settings
            files = {
                "prompt": (None, enhanced_prompt),
                "resolution": (None, ideogram_size),
                "rendering_speed": (None, "QUALITY"),  # Use QUALITY for best text rendering
                "magic_prompt": (None, "AUTO"),  # Use AUTO for better magic prompt enhancement
                "style_type": (None, "DESIGN"),  # DESIGN style is optimal for text-heavy advertisements
                "negative_prompt": (None, "blurry text, illegible text, distorted text, unreadable text, poor typography, bad font, unclear letters, pixelated text, low quality text, amateur typography, unprofessional text, watermark, signature, low resolution, blurry, distorted, poor quality, amateur design, bad composition, unclear, fuzzy text, messy text, pixelated, low contrast text, hard to read text"),
                "num_images": (None, str(num_images))  # Generate multiple images
            }

            logger.info(f"🎨 Requesting {num_images} images from Ideogram API...")
            logger.info(f"📝 Prompt length: {len(enhanced_prompt)} characters")
            logger.info(f"🎯 Using DESIGN style with QUALITY rendering for optimal text")

            headers = {
                "Api-Key": self.api_key
            }

            logger.info(f"🎨 Generating {num_images} advertisements with Ideogram.ai: {prompt[:100]}...")

            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    f"{self.base_url}/generate",
                    files=files,
                    headers=headers
                )

                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"Ideogram error {response.status_code}: {error_text}")
                    return {"success": False, "error": f"Ideogram error: {error_text}"}

                result = response.json()

                # Process multiple images from response
                if "data" in result and len(result["data"]) > 0:
                    images = []
                    logger.info(f"📊 Processing {len(result['data'])} images from Ideogram response")

                    for i, image_data in enumerate(result["data"]):
                        image_url = image_data.get("url")
                        if image_url:
                            logger.info(f"✅ Image {i+1}: {image_url[:50]}...")
                            images.append({
                                "image_url": image_url,
                                "revised_prompt": image_data.get("prompt"),
                                "metadata": {
                                    "model": "ideogram-3.0-quality",
                                    "size": size,
                                    "resolution": ideogram_size,
                                    "original_prompt": prompt,
                                    "enhanced_prompt": enhanced_prompt,
                                    "seed": image_data.get("seed"),
                                    "variation": i + 1,
                                    "is_image_safe": image_data.get("is_image_safe", True),
                                    "style_type": image_data.get("style_type", "DESIGN")
                                }
                            })
                        else:
                            logger.warning(f"⚠️ Image {i+1} has no URL: {image_data}")

                    if images:
                        # Extract URLs for compatibility
                        image_urls = [img["image_url"] for img in images]
                        logger.info(f"🎉 Successfully processed {len(images)} images out of {len(result['data'])} received")

                        return {
                            "success": True,
                            "image_url": image_urls[0],  # First image as primary
                            "all_images": image_urls,
                            "num_generated": len(images),
                            "revised_prompt": images[0]["revised_prompt"],
                            "metadata": {
                                "model": "ideogram-3.0-quality",
                                "size": size,
                                "resolution": ideogram_size,
                                "original_prompt": prompt,
                                "enhanced_prompt": enhanced_prompt,
                                "num_requested": num_images,
                                "num_generated": len(images),
                                "type": "premium_batch_generation",
                                "rendering_speed": "QUALITY",
                                "magic_prompt": "AUTO",
                                "style_type": "DESIGN",
                                "all_images_data": images
                            }
                        }
                    else:
                        logger.error("❌ No valid images found in response data")
                        return {"success": False, "error": "No valid images in response"}
                else:
                    logger.error(f"❌ Invalid response structure: {result}")
                    return {"success": False, "error": "No image data in response"}

        except Exception as e:
            logger.error(f"Error generating multiple advertisements with Ideogram: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}

    async def generate_with_reference(self, prompt: str, reference_image: UploadFile, size: str = "1024x1024") -> Dict[str, Any]:
        """
        Generate photographic image using a reference image with Ideogram.ai.
        Optimized for camera-like realism and photographic style transfer.

        Args:
            prompt: Description of the photograph to create
            reference_image: Reference image to guide the photographic generation
            size: Image size

        Returns:
            Dict with success status, image data, and metadata
        """
        if not self.api_key:
            logger.error("Ideogram API key not configured")
            return {"success": False, "error": "Ideogram API key not configured"}

        try:
            # Read reference image content
            image_content = await reference_image.read()

            # Enhanced photographic prompt for reference-based generation
            enhanced_prompt = f"""Professional photography inspired by the reference image: {prompt}

PHOTOGRAPHIC STYLE ADAPTATION:
- Capture the lighting style, composition, and photographic aesthetic from the reference
- Maintain authentic camera-captured quality and realism
- Use similar depth of field, focus, and photographic techniques
- Adapt the color grading, contrast, and mood from reference
- Keep the professional photography standards and visual appeal

CAMERA REALISM:
- Shot with professional camera equipment
- Natural photographic lighting and shadows
- Authentic depth of field and focus
- Realistic camera perspective and framing
- High-quality photographic composition

The result should be a genuine photograph that captures the style essence of the reference while being completely photorealistic."""

            size_mapping = {
                "1024x1024": "1024x1024",
                "1024x1792": "1024x1792",
                "1792x1024": "1792x1024",
                "auto": "1024x1024"
            }

            ideogram_size = size_mapping.get(size, "1024x1024")

            # Prepare multipart form data with reference image for photographic generation
            files = {
                "prompt": (None, enhanced_prompt),
                "resolution": (None, ideogram_size),
                "rendering_speed": (None, "QUALITY"),  # Highest quality for photography
                "magic_prompt": (None, "ON"),  # ENABLE magic prompt for photographic enhancement
                "style_type": (None, "REALISTIC"),  # Use REALISTIC style for camera-like images
                "negative_prompt": (None, "digital art, illustration, cartoon, anime, painting, drawing, sketch, CGI, 3D render, artificial, synthetic, fake, unrealistic, low quality, blurry, pixelated, amateur, unprofessional, poor lighting, bad composition"),
                "num_images": (None, "1"),
                "style_reference_images": ("reference.png", image_content, "image/png")
            }

            headers = {
                "Api-Key": self.api_key
            }

            logger.info(f"� Generating photographic image with reference using Ideogram.ai: {prompt[:100]}...")

            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    f"{self.base_url}/generate",
                    files=files,
                    headers=headers
                )
                
                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"Ideogram reference error {response.status_code}: {error_text}")
                    return {"success": False, "error": f"Ideogram error: {error_text}"}
                
                result = response.json()
                
                if "data" in result and len(result["data"]) > 0:
                    image_data = result["data"][0]
                    image_url = image_data.get("url")

                    if image_url:
                        return {
                            "success": True,
                            "image_url": image_url,
                            "revised_prompt": image_data.get("prompt"),
                            "metadata": {
                                "model": "ideogram-3.0-quality",
                                "size": size,
                                "resolution": ideogram_size,
                                "original_prompt": prompt,
                                "enhanced_prompt": enhanced_prompt,
                                "seed": image_data.get("seed"),
                                "type": "reference_based",
                                "reference_used": True,
                                "is_image_safe": image_data.get("is_image_safe", True),
                                "style_type": image_data.get("style_type", "GENERAL")
                            }
                        }
                    else:
                        return {"success": False, "error": "No image URL in reference response"}
                else:
                    return {"success": False, "error": "No image data in reference response"}
                    
        except Exception as e:
            logger.error(f"Error in reference generation with Ideogram: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}

    async def generate_similar_image_with_reference(self, prompt: str, reference_image_url: str, reference_metadata: Dict[str, Any], size: str = "1024x1024") -> Dict[str, Any]:
        """
        Generate an image similar to a reference using Ideogram's style_reference_images parameter.
        Uses /generate endpoint with style_reference_images for similar (not remix) generation.

        Args:
            prompt: Description of the image to create
            reference_image_url: URL of the reference image to use as style guide
            reference_metadata: Metadata from the reference image
            size: Image size

        Returns:
            Dict with success status, image data, and metadata
        """
        if not self.api_key:
            logger.error("Ideogram API key not configured")
            return {"success": False, "error": "Ideogram API key not configured"}

        try:
            # Try to get image from local storage first
            from app.services.image_storage_service import image_storage_service

            reference_image_content = image_storage_service.read_stored_image(reference_image_url)

            if reference_image_content:
                logger.info(f"📁 Using locally stored reference image")
            else:
                # Fallback: try to download directly (will likely fail due to expiration)
                logger.warning(f"⚠️ Reference image not found in local storage, trying direct download")
                import httpx
                async with httpx.AsyncClient() as client:
                    response = await client.get(reference_image_url)
                    if response.status_code != 200:
                        logger.error(f"Failed to download reference image: {response.status_code}")
                        return {"success": False, "error": "Failed to download reference image - URL may have expired"}
                    reference_image_content = response.content

            # Extract reference parameters
            reference_style_type = reference_metadata.get("style_type", "DESIGN")

            # Map common sizes to Ideogram format
            size_mapping = {
                "1024x1024": "1024x1024",
                "1024x1792": "1024x1792",
                "1792x1024": "1792x1024",
                "auto": "1024x1024"
            }

            ideogram_size = size_mapping.get(size, "1024x1024")

            # Prepare multipart form data with style_reference_images (CORRECT WAY FOR SIMILAR)
            files = {
                "prompt": (None, prompt),
                "resolution": (None, ideogram_size),
                "rendering_speed": (None, "QUALITY"),
                "magic_prompt": (None, "AUTO"),
                "style_type": (None, reference_style_type),
                "negative_prompt": (None, "photorealistic humans, realistic people, photography of people, realistic portraits, realistic faces, human photography, portrait photography, realistic human figures, photographic style people, realistic man, realistic woman, lifelike humans, real person photos, selfie, crowd photography, blurry text, illegible text, distorted text, unreadable text, poor typography, low quality, pixelated, amateur, unprofessional, poor lighting, bad composition, watermark, low resolution"),
                "num_images": (None, "1"),
                "style_reference_images": ("reference.png", reference_image_content, "image/png")  # Style reference for similar images
            }

            headers = {
                "Api-Key": self.api_key
            }

            logger.info(f"🎨 Generating SIMILAR image using style_reference_images: {prompt[:100]}...")
            logger.info(f"🖼️ Using /generate with style_reference_images (official documentation)")

            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    f"{self.base_url}/generate",  # Use /generate endpoint for similar
                    files=files,
                    headers=headers
                )

                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"Ideogram similar image error {response.status_code}: {error_text}")
                    return {"success": False, "error": f"Ideogram error: {error_text}"}

                result = response.json()

                # Ideogram 3.0 response format
                if "data" in result and len(result["data"]) > 0:
                    image_data = result["data"][0]
                    image_url = image_data.get("url")

                    if image_url:
                        return {
                            "success": True,
                            "image_url": image_url,
                            "revised_prompt": image_data.get("prompt"),
                            "metadata": {
                                "model": "ideogram-3.0-quality",
                                "size": size,
                                "resolution": ideogram_size,
                                "original_prompt": prompt,
                                "seed": image_data.get("seed"),
                                "type": "style_reference_based",
                                "reference_used": True,
                                "is_image_safe": image_data.get("is_image_safe", True),
                                "style_type": image_data.get("style_type", reference_style_type),
                                "similarity_method": "style_reference_images"
                            }
                        }
                    else:
                        return {"success": False, "error": "No image URL in similar image response"}
                else:
                    return {"success": False, "error": "No image data in similar image response"}

        except Exception as e:
            logger.error(f"Error generating similar image with style reference: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}

    async def generate_image_with_seed(self, prompt: str, seed: int, style_type: str = "DESIGN", size: str = "1024x1024") -> Dict[str, Any]:
        """
        Generate an image using a specific seed for consistency.
        This is a more reliable approach for generating similar images.

        Args:
            prompt: Description of the image to create
            seed: Seed value for consistent generation
            style_type: Style type (DESIGN, GENERAL, etc.)
            size: Image size

        Returns:
            Dict with success status, image data, and metadata
        """
        if not self.api_key:
            logger.error("Ideogram API key not configured")
            return {"success": False, "error": "Ideogram API key not configured"}

        try:
            # Map common sizes to Ideogram format
            size_mapping = {
                "1024x1024": "1024x1024",
                "1024x1792": "1024x1792",
                "1792x1024": "1792x1024",
                "auto": "1024x1024"
            }

            ideogram_size = size_mapping.get(size, "1024x1024")

            # Prepare multipart form data with seed
            files = {
                "prompt": (None, prompt),
                "resolution": (None, ideogram_size),
                "rendering_speed": (None, "QUALITY"),
                "magic_prompt": (None, "AUTO"),
                "style_type": (None, style_type),
                "seed": (None, str(seed)),  # Use specific seed for similarity
                "negative_prompt": (None, "photorealistic humans, realistic people, photography of people, realistic portraits, realistic faces, human photography, portrait photography, realistic human figures, photographic style people, realistic man, realistic woman, lifelike humans, real person photos, selfie, crowd photography, blurry text, illegible text, distorted text, unreadable text, poor typography, low quality, pixelated, amateur, unprofessional, poor lighting, bad composition, watermark, low resolution"),
                "num_images": (None, "1")
            }

            headers = {
                "Api-Key": self.api_key
            }

            logger.info(f"🌱 Generating image with seed {seed}: {prompt[:100]}...")

            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    f"{self.base_url}/generate",
                    files=files,
                    headers=headers
                )

                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"Ideogram seed-based error {response.status_code}: {error_text}")
                    return {"success": False, "error": f"Ideogram error: {error_text}"}

                result = response.json()

                # Ideogram 3.0 response format
                if "data" in result and len(result["data"]) > 0:
                    image_data = result["data"][0]
                    image_url = image_data.get("url")

                    if image_url:
                        return {
                            "success": True,
                            "image_url": image_url,
                            "revised_prompt": image_data.get("prompt"),
                            "metadata": {
                                "model": "ideogram-3.0-quality",
                                "size": size,
                                "resolution": ideogram_size,
                                "original_prompt": prompt,
                                "seed": image_data.get("seed", seed),
                                "style_type": image_data.get("style_type", style_type),
                                "is_similar_to_reference": True,
                                "similarity_method": "seed_based",
                                "is_image_safe": image_data.get("is_image_safe", True)
                            }
                        }
                    else:
                        logger.error(f"No image URL found in seed-based response: {image_data}")
                        return {"success": False, "error": "No image URL in response"}
                else:
                    logger.error(f"No data array in seed-based response: {result}")
                    return {"success": False, "error": "No image data in response"}

        except Exception as e:
            logger.error(f"Error generating image with seed: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}


# Global service instance
ideogram_service = IdeogramService()
