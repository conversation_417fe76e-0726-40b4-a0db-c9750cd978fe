"use client"

import { useEffect, useState } from "react"
import { <PERSON> } from "wouter"
import { motion, LayoutGroup } from "framer-motion"
import { TextRotate } from "@/components/ui/text-rotate"
import { But<PERSON> } from "@/components/ui/button"
import Floating, { FloatingElement } from "@/components/ui/parallax-floating"

const emmaImages = [
  {
    url: "/Anuncio.png",
    title: "Anuncio Publicitario",
    alt: "Anuncio"
  },
  {
    url: "/Astronauta.png",
    title: "Astronauta",
    alt: "Astronauta"
  },
  {
    url: "/Gato lentes.webp",
    title: "Gato con Lentes",
    alt: "Gato Lentes"
  },
  {
    url: "/Hombre real.jpeg",
    title: "Hombre Real",
    alt: "Hombre Real"
  },
  {
    url: "/Labial.jpg",
    title: "Labial",
    alt: "Labial"
  },
  {
    url: "/PHOTO-2025-06-30-19-02-40.jpg",
    title: "Foto Profesional",
    alt: "Foto Profesional"
  },
  {
    url: "/libro.jpg",
    title: "Libro",
    alt: "Libro"
  },
  {
    url: "/poster.jpg",
    title: "Poster",
    alt: "Poster"
  }
]

export function HeroSection() {
  return (
    <section className="w-full h-screen overflow-hidden md:overflow-visible flex flex-col items-center justify-center relative bg-white">
      {/* Floating Images Background */}
      <Floating sensitivity={-0.5} className="absolute inset-0 w-full h-full z-10 pointer-events-none" style={{ isolation: 'isolate' }}>
        {/* Floating Images */}
        <FloatingElement
          depth={0.5}
          className="z-[11]"
          style={{ top: '10%', left: '5%' }}
        >
          <motion.img
            src={emmaImages[0].url}
            alt={emmaImages[0].alt}
            className="w-20 h-16 sm:w-28 sm:h-20 md:w-32 md:h-24 lg:w-36 lg:h-28 object-cover hover:scale-105 duration-200 cursor-pointer transition-transform -rotate-[3deg] shadow-2xl rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
          />
        </FloatingElement>

        <FloatingElement
          depth={1}
          className="z-[12]"
          style={{ top: '5%', left: '15%' }}
        >
          <motion.img
            src={emmaImages[1].url}
            alt={emmaImages[1].alt}
            className="w-32 h-24 sm:w-40 sm:h-28 md:w-48 md:h-36 lg:w-56 lg:h-40 object-cover hover:scale-105 duration-200 cursor-pointer transition-transform -rotate-12 shadow-2xl rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.7 }}
          />
        </FloatingElement>

        <FloatingElement
          depth={4}
          className="z-[15]"
          style={{ top: '75%', left: '8%' }}
        >
          <motion.img
            src={emmaImages[2].url}
            alt={emmaImages[2].alt}
            className="w-36 h-36 sm:w-44 sm:h-44 md:w-52 md:h-52 lg:w-60 lg:h-60 object-cover -rotate-[4deg] hover:scale-105 duration-200 cursor-pointer transition-transform shadow-2xl rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.9 }}
          />
        </FloatingElement>

        <FloatingElement
          depth={2}
          className="z-[13]"
          style={{ top: '8%', right: '5%' }}
        >
          <motion.img
            src={emmaImages[3].url}
            alt={emmaImages[3].alt}
            className="w-32 h-28 sm:w-40 sm:h-32 md:w-48 md:h-40 lg:w-56 lg:h-44 object-cover hover:scale-105 duration-200 cursor-pointer transition-transform shadow-2xl rotate-[6deg] rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.1 }}
          />
        </FloatingElement>

        <FloatingElement
          depth={1}
          className="z-[12]"
          style={{ top: '70%', right: '8%' }}
        >
          <motion.img
            src={emmaImages[4].url}
            alt={emmaImages[4].alt}
            className="w-40 h-40 sm:w-52 sm:h-52 md:w-64 md:h-64 lg:w-72 lg:h-72 object-cover hover:scale-105 duration-200 cursor-pointer transition-transform shadow-2xl rotate-[19deg] rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.3 }}
          />
        </FloatingElement>

        <FloatingElement
          depth={3}
          className="z-[14]"
          style={{ top: '45%', left: '2%' }}
        >
          <motion.img
            src={emmaImages[5].url}
            alt={emmaImages[5].alt}
            className="w-24 h-20 sm:w-32 sm:h-24 md:w-40 md:h-32 lg:w-44 lg:h-36 object-cover hover:scale-105 duration-200 cursor-pointer transition-transform shadow-2xl rotate-[8deg] rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.5 }}
          />
        </FloatingElement>

        <FloatingElement
          depth={2.5}
          className="z-[13]"
          style={{ top: '25%', right: '15%' }}
        >
          <motion.img
            src={emmaImages[6].url}
            alt={emmaImages[6].alt}
            className="w-28 h-24 sm:w-36 sm:h-28 md:w-44 md:h-36 lg:w-52 lg:h-40 object-cover hover:scale-105 duration-200 cursor-pointer transition-transform shadow-2xl -rotate-[7deg] rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.7 }}
          />
        </FloatingElement>

        <FloatingElement
          depth={1.5}
          className="z-[12]"
          style={{ top: '55%', right: '25%' }}
        >
          <motion.img
            src={emmaImages[7].url}
            alt={emmaImages[7].alt}
            className="w-32 h-32 sm:w-40 sm:h-40 md:w-48 md:h-48 lg:w-56 lg:h-56 object-cover hover:scale-105 duration-200 cursor-pointer transition-transform shadow-2xl rotate-[15deg] rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.9 }}
          />
        </FloatingElement>

        <FloatingElement
          depth={3.5}
          className="z-[15]"
          style={{ top: '15%', left: '25%' }}
        >
          <motion.video
            autoPlay
            loop
            muted
            playsInline
            className="w-28 h-28 sm:w-36 sm:h-36 md:w-44 md:h-44 lg:w-52 lg:h-52 object-cover hover:scale-105 duration-200 cursor-pointer transition-transform shadow-2xl -rotate-[12deg] rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 2.1 }}
          >
            <source src="/video gato brincando.mp4" type="video/mp4" />
          </motion.video>
        </FloatingElement>
      </Floating>

      {/* Main Content */}
      <div className="flex flex-col justify-center items-center w-[250px] sm:w-[300px] md:w-[500px] lg:w-[700px] z-50 relative pointer-events-auto">
        <motion.h1
          className="text-3xl sm:text-5xl md:text-7xl lg:text-8xl text-center w-full justify-center items-center flex-col flex whitespace-pre leading-tight font-bold tracking-tight space-y-1 md:space-y-4"
          animate={{ opacity: 1, y: 0 }}
          initial={{ opacity: 0, y: 20 }}
          transition={{ duration: 0.2, ease: "easeOut", delay: 0.3 }}
        >
          <span>Haz tu </span>
          <LayoutGroup>
            <motion.span layout className="flex whitespace-pre">
              <motion.span
                layout
                className="flex whitespace-pre"
                transition={{ type: "spring", damping: 30, stiffness: 400 }}
              >
                marketing{" "}
              </motion.span>
              <TextRotate
                texts={[
                  "increíble",
                  "viral",
                  "exitoso ✨",
                  "único",
                  "🚀 potente",
                  "💎 premium",
                  "profesional",
                  "🔥 épico",
                  "imparable",
                  "⚡ rápido",
                  "automático",
                  "inteligente",
                  "rentable 💰",
                ]}
                mainClassName="overflow-hidden pr-3 text-[#3018ef] py-0 pb-2 md:pb-4 rounded-xl"
                staggerDuration={0.03}
                staggerFrom="last"
                rotationInterval={3000}
                transition={{ type: "spring", damping: 30, stiffness: 400 }}
              />
            </motion.span>
          </LayoutGroup>
        </motion.h1>

        <motion.p
          className="text-sm sm:text-lg md:text-xl lg:text-2xl text-center font-normal pt-4 sm:pt-8 md:pt-10 lg:pt-12 text-gray-600"
          animate={{ opacity: 1, y: 0 }}
          initial={{ opacity: 0, y: 20 }}
          transition={{ duration: 0.2, ease: "easeOut", delay: 0.5 }}
        >
          con la primera agencia virtual de marketing del mundo.
          <br />
          IA especializada, resultados reales, disponible 24/7.
        </motion.p>

        <div className="flex flex-row justify-center space-x-4 items-center mt-10 sm:mt-16 md:mt-20 lg:mt-20 text-xs">
          <motion.div
            animate={{ opacity: 1, y: 0 }}
            initial={{ opacity: 0, y: 20 }}
            transition={{
              duration: 0.2,
              ease: "easeOut",
              delay: 0.7,
              scale: { duration: 0.2 },
            }}
            whileHover={{
              scale: 1.05,
              transition: { type: "spring", damping: 30, stiffness: 400 },
            }}
          >
            <Link href="/login">
              <Button variant="red" size="lg" className="sm:text-base md:text-lg lg:text-xl font-semibold tracking-tight px-4 py-2 sm:px-5 sm:py-2.5 md:px-6 md:py-3 lg:px-8 lg:py-3 rounded-full z-20 shadow-2xl">
                Empezar Ahora <span className="font-serif ml-1">→</span>
              </Button>
            </Link>
          </motion.div>

          <motion.div
            animate={{ opacity: 1, y: 0 }}
            initial={{ opacity: 0, y: 20 }}
            transition={{
              duration: 0.2,
              ease: "easeOut",
              delay: 0.7,
              scale: { duration: 0.2 },
            }}
            whileHover={{
              scale: 1.05,
              transition: { type: "spring", damping: 30, stiffness: 400 },
            }}
          >
            <Link href="#demo">
              <Button variant="blue" size="lg" className="sm:text-base md:text-lg lg:text-xl font-semibold tracking-tight px-4 py-2 sm:px-5 sm:py-2.5 md:px-6 md:py-3 lg:px-8 lg:py-3 rounded-full z-20 shadow-2xl">
                ★ Ver Demo
              </Button>
            </Link>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
