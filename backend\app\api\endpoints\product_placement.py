"""
API endpoints for Product Placement functionality.
Generates background scenes and composites products for marketing mockups.
"""

import logging
from fastapi import APIRouter, HTTPException, UploadFile, File
from typing import Dict, Any, Optional
from pydantic import BaseModel

from app.services.product_placement_service import product_placement_service

logger = logging.getLogger(__name__)
router = APIRouter()


# Pydantic models for request/response
class BackgroundGenerationRequest(BaseModel):
    scene_prompt: str
    style: str = "realistic"
    size: str = "1024x1024"
    product_image_data: Optional[str] = None  # Optional product image for style reference


class ProductCompositeRequest(BaseModel):
    background_url: str
    product_image_data: str
    position: Dict[str, float]  # {"x": 0.5, "y": 0.5}
    scale: float = 1.0
    rotation: float = 0.0
    shadow_intensity: float = 0.3


class ProductPlacementResponse(BaseModel):
    success: bool
    image_data: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


@router.post(
    "/generate-background",
    response_model=ProductPlacementResponse,
)
async def generate_background_scene(request: BackgroundGenerationRequest):
    """
    Generar escena de fondo para colocación de productos usando Ideogram AI.
    """
    try:
        logger.info(f"🎨 Generating background scene: {request.scene_prompt}")
        
        result = await product_placement_service.generate_background_scene(
            scene_prompt=request.scene_prompt,
            style=request.style,
            size=request.size,
            product_image_data=request.product_image_data
        )
        
        return ProductPlacementResponse(**result)
        
    except Exception as e:
        logger.error(f"Error generating background scene: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error generando escena de fondo: {str(e)}"
        )


@router.post(
    "/upload-product",
    response_model=ProductPlacementResponse,
)
async def upload_product_image(
    product_image: UploadFile = File(..., description="Imagen del producto a colocar")
):
    """
    Subir y procesar imagen de producto para colocación.
    """
    try:
        logger.info(f"📦 Processing product image: {product_image.filename}")
        
        result = await product_placement_service.process_product_image(product_image)
        
        return ProductPlacementResponse(**result)
        
    except Exception as e:
        logger.error(f"Error processing product image: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error procesando imagen de producto: {str(e)}"
        )


@router.post(
    "/composite",
    response_model=ProductPlacementResponse,
)
async def composite_product_on_background(request: ProductCompositeRequest):
    """
    Componer producto sobre fondo con posicionamiento y efectos.
    """
    try:
        logger.info(f"🖼️ Compositing product on background")
        
        # Validate position values
        if not (0 <= request.position.get("x", 0.5) <= 1):
            raise HTTPException(status_code=400, detail="Position X debe estar entre 0 y 1")
        if not (0 <= request.position.get("y", 0.5) <= 1):
            raise HTTPException(status_code=400, detail="Position Y debe estar entre 0 y 1")
        
        # Validate scale
        if not (0.1 <= request.scale <= 2.0):
            raise HTTPException(status_code=400, detail="Scale debe estar entre 0.1 y 2.0")
        
        # Validate shadow intensity
        if not (0 <= request.shadow_intensity <= 1):
            raise HTTPException(status_code=400, detail="Shadow intensity debe estar entre 0 y 1")
        
        result = await product_placement_service.composite_product_on_background(
            background_url=request.background_url,
            product_image_data=request.product_image_data,
            position=request.position,
            scale=request.scale,
            rotation=request.rotation,
            shadow_intensity=request.shadow_intensity
        )
        
        return ProductPlacementResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error compositing product: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error creando composición: {str(e)}"
        )


@router.get("/scene-suggestions")
async def get_scene_suggestions():
    """
    Obtener sugerencias predefinidas de escenas para colocación de productos.
    """
    try:
        suggestions = await product_placement_service.get_scene_suggestions()
        
        return {
            "success": True,
            "suggestions": suggestions
        }
        
    except Exception as e:
        logger.error(f"Error getting scene suggestions: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error obteniendo sugerencias: {str(e)}"
        )


@router.get("/styles")
async def get_available_styles():
    """
    Obtener estilos disponibles para generación de fondos.
    """
    styles = [
        {
            "id": "realistic",
            "name": "Realista",
            "description": "Fotografía profesional realista con iluminación natural"
        },
        {
            "id": "minimalist",
            "name": "Minimalista",
            "description": "Fondo limpio y minimalista con colores neutros"
        },
        {
            "id": "lifestyle",
            "name": "Lifestyle",
            "description": "Ambiente natural y auténtico estilo de vida"
        },
        {
            "id": "commercial",
            "name": "Comercial",
            "description": "Configuración de estudio profesional para marketing"
        }
    ]
    
    return {
        "success": True,
        "styles": styles
    }


@router.get("/health")
async def health_check():
    """
    Verificar estado del servicio de Product Placement.
    """
    return {
        "success": True,
        "service": "Product Placement",
        "status": "healthy",
        "features": [
            "background_generation",
            "product_processing",
            "image_compositing",
            "scene_suggestions"
        ]
    }
