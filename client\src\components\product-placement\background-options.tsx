import React from "react";
import { ImageIcon } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

interface BackgroundOption {
  url: string;
  data: string;
  seed: number;
  prompt: string;
  is_image_safe: boolean;
  style_type: string;
}

interface BackgroundOptionsProps {
  backgroundOptions: BackgroundOption[];
  selectedBackgroundIndex: number;
  onSelectBackground: (index: number) => void;
}

export function BackgroundOptions({
  backgroundOptions,
  selectedBackgroundIndex,
  onSelectBackground,
}: BackgroundOptionsProps) {
  if (backgroundOptions.length <= 1) {
    return null;
  }

  return (
    <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-slate-800">
          <ImageIcon className="w-5 h-5 text-[#dd3a5a]" />
          Opciones de Fondo ({backgroundOptions.length})
        </CardTitle>
        <CardDescription>
          Selecciona tu opción favorita de las generadas
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-3">
          {backgroundOptions.map((option, index) => (
            <div
              key={index}
              className={`relative cursor-pointer rounded-lg overflow-hidden border-2 transition-all hover:scale-105 ${
                selectedBackgroundIndex === index
                  ? 'border-[#3018ef] shadow-lg'
                  : 'border-slate-200 hover:border-[#dd3a5a]'
              }`}
              onClick={() => onSelectBackground(index)}
            >
              <img
                src={option.data}
                alt={`Opción ${index + 1}`}
                className="w-full h-24 object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
              <div className="absolute bottom-1 left-1 right-1">
                <div className="text-white text-xs font-medium">
                  Opción {index + 1}
                </div>
                {selectedBackgroundIndex === index && (
                  <div className="text-[#3018ef] text-xs font-bold">
                    ✓ Seleccionada
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
