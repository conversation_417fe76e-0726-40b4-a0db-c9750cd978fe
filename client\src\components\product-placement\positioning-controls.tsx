import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Loader2 } from "lucide-react";
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Label } from "@/components/ui/label";

interface PositioningControlsProps {
  position: { x: number; y: number };
  scale: number;
  rotation: number;
  shadowIntensity: number;
  isCompositing: boolean;
  onPositionChange: (position: { x: number; y: number }) => void;
  onScaleChange: (scale: number) => void;
  onRotationChange: (rotation: number) => void;
  onShadowIntensityChange: (intensity: number) => void;
  onComposite: () => void;
}

export function PositioningControls({
  position,
  scale,
  rotation,
  shadowIntensity,
  isCompositing,
  onPositionChange,
  onScaleChange,
  onRotationChange,
  onShadowIntensityChange,
  onComposite,
}: PositioningControlsProps) {
  return (
    <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-slate-800">
          <Settings className="w-5 h-5 text-[#3018ef]" />
          Controles de Posición
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Position */}
        <div>
          <Label className="text-sm font-medium text-slate-700">Posición</Label>
          <div className="grid grid-cols-2 gap-4 mt-2">
            <div>
              <Label className="text-xs text-slate-600">X (Horizontal)</Label>
              <Slider
                value={[position.x]}
                onValueChange={([x]) => onPositionChange({ ...position, x })}
                min={0}
                max={1}
                step={0.01}
                className="mt-1"
              />
            </div>
            <div>
              <Label className="text-xs text-slate-600">Y (Vertical)</Label>
              <Slider
                value={[position.y]}
                onValueChange={([y]) => onPositionChange({ ...position, y })}
                min={0}
                max={1}
                step={0.01}
                className="mt-1"
              />
            </div>
          </div>
        </div>

        {/* Scale */}
        <div>
          <Label className="text-sm font-medium text-slate-700">
            Escala ({Math.round(scale * 100)}%)
          </Label>
          <Slider
            value={[scale]}
            onValueChange={([scale]) => onScaleChange(scale)}
            min={0.1}
            max={2.0}
            step={0.1}
            className="mt-2"
          />
        </div>

        {/* Rotation */}
        <div>
          <Label className="text-sm font-medium text-slate-700">
            Rotación ({rotation}°)
          </Label>
          <Slider
            value={[rotation]}
            onValueChange={([rotation]) => onRotationChange(rotation)}
            min={-180}
            max={180}
            step={5}
            className="mt-2"
          />
        </div>

        {/* Shadow */}
        <div>
          <Label className="text-sm font-medium text-slate-700">
            Intensidad de Sombra ({Math.round(shadowIntensity * 100)}%)
          </Label>
          <Slider
            value={[shadowIntensity]}
            onValueChange={([shadowIntensity]) => onShadowIntensityChange(shadowIntensity)}
            min={0}
            max={1}
            step={0.1}
            className="mt-2"
          />
        </div>

        {/* Composite Button */}
        <Button
          className="w-full bg-gradient-to-r from-[#dd3a5a] to-[#3018ef] hover:opacity-90 hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl"
          onClick={onComposite}
          disabled={isCompositing}
        >
          {isCompositing ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Creando Composición...
            </>
          ) : (
            <>
              <Palette className="w-4 h-4 mr-2" />
              Crear Composición
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  );
}
