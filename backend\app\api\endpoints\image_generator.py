"""
Image Generator API endpoints using Ideogram AI v3.
Based on posters.py but adapted for general image generation without prompt modifications.
"""

from fastapi import APIRouter, Form, File, UploadFile, HTTPException, Query
from fastapi.responses import Response
from typing import List, Optional
import logging
import httpx

from app.services.image_generator_service import image_generator_service
from app.schemas.poster import FrontendPosterResponse

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(
    tags=["image-generator"],
    responses={
        404: {"description": "Not found"},
        500: {"description": "Internal server error"}
    }
)


@router.post("/generate")
async def generate_image(
    prompt: str = Form(..., description="Description of the image to create"),
    resolution: Optional[str] = Form(None, description="Ideogram resolution (e.g., '1024x1024')"),
    aspect_ratio: Optional[str] = Form(None, description="Aspect ratio (e.g., '1:1')"),
    rendering_speed: str = Form(default="DEFAULT", description="Rendering speed: TURBO/DEFAULT/QUALITY"),
    magic_prompt: str = Form(default="AUTO", description="Magic prompt: AUTO/ON/OFF"),
    negative_prompt: Optional[str] = Form(None, description="What to exclude from image"),
    num_images: int = Form(default=1, description="Number of images to generate (1-8)"),
    style_type: str = Form(default="GENERAL", description="Style type: AUTO/GENERAL/REALISTIC/DESIGN")
) -> FrontendPosterResponse:
    """Generate an image using Ideogram AI v3 model."""

    try:
        logger.info(f"🎨 Generating image with Ideogram: {prompt[:100]}...")

        # Call the service with all parameters
        service_response = await image_generator_service.generate_image(
            prompt=prompt,
            resolution=resolution,
            aspect_ratio=aspect_ratio,
            rendering_speed=rendering_speed,
            magic_prompt=magic_prompt,
            negative_prompt=negative_prompt,
            num_images=num_images,
            style_type=style_type
        )

        # Convert to frontend response
        return FrontendPosterResponse.from_service_response(service_response)

    except Exception as e:
        logger.error(f"Error in generate_image endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during image generation: {e}"
        )


@router.post("/generate-photography")
async def generate_photography(
    prompt: str = Form(..., description="Description of the photograph to create"),
    size: str = Form(default="1024x1024", description="Image size (1024x1024, 1024x1792, 1792x1024)"),
    num_images: int = Form(default=4, description="Number of photographic variations to generate (1-8)")
) -> FrontendPosterResponse:
    """Generate photographic images optimized for camera-like realism using Ideogram AI."""

    try:
        logger.info(f"📸 Generating photography with Ideogram: {prompt[:100]}...")

        # Import the ideogram service directly for photography
        from app.services.ideogram_service import ideogram_service

        # Call the photography-optimized method
        service_response = await ideogram_service.generate_photography(
            prompt=prompt,
            size=size,
            num_images=num_images
        )

        # Convert to frontend response
        return FrontendPosterResponse.from_service_response(service_response)

    except Exception as e:
        logger.error(f"Error in generate_photography endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during photography generation: {e}"
        )


@router.post("/multi-turn-edit")
async def multi_turn_edit_image(
    previous_response_id: str = Form(..., description="ID of the previous response to build upon"),
    edit_prompt: str = Form(..., description="Description of the changes to make")
) -> FrontendPosterResponse:
    """Edit an existing image using multi-turn generation."""
    
    try:
        logger.info(f"🔄 Multi-turn editing image: {edit_prompt[:100]}...")
        
        # Call the service
        service_response = await image_generator_service.multi_turn_edit(
            previous_response_id=previous_response_id,
            edit_prompt=edit_prompt
        )
        
        # Convert to frontend response
        return FrontendPosterResponse.from_service_response(service_response)
        
    except Exception as e:
        logger.error(f"Error in multi_turn_edit_image endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during multi-turn editing: {e}"
        )


@router.post("/edit-with-references")
async def edit_image_with_references(
    prompt: str = Form(..., description="Description of the image to create"),
    reference_images: List[UploadFile] = File(..., description="Reference images (max 4)"),
    resolution: Optional[str] = Form(None, description="Ideogram resolution (e.g., '1024x1024')"),
    aspect_ratio: Optional[str] = Form(None, description="Aspect ratio (e.g., '1:1')")
) -> FrontendPosterResponse:
    """Generate image using reference images."""
    
    try:
        logger.info(f"🖼️ Generating image using {len(reference_images)} references: {prompt[:100]}...")
        
        # Validate reference images count
        if len(reference_images) > 4:
            raise HTTPException(
                status_code=400,
                detail="Maximum 4 reference images allowed"
            )
        
        # Call the service
        service_response = await image_generator_service.edit_with_references(
            prompt=prompt,
            reference_images=reference_images,
            resolution=resolution,
            aspect_ratio=aspect_ratio
        )
        
        # Convert to frontend response
        return FrontendPosterResponse.from_service_response(service_response)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in edit_image_with_references endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during reference-based generation: {e}"
        )


@router.post("/edit-with-mask")
async def edit_image_with_mask(
    prompt: str = Form(..., description="Description of the changes to make"),
    image: UploadFile = File(..., description="Original image file"),
    mask: UploadFile = File(..., description="Mask image file (white areas will be edited)")
) -> FrontendPosterResponse:
    """Edit image using mask-based editing."""
    
    try:
        logger.info(f"🎭 Editing image using mask: {prompt[:100]}...")
        
        # Call the service
        service_response = await image_generator_service.edit_with_mask(
            prompt=prompt,
            image=image,
            mask=mask
        )
        
        # Convert to frontend response
        return FrontendPosterResponse.from_service_response(service_response)
        
    except Exception as e:
        logger.error(f"Error in edit_image_with_mask endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during mask-based editing: {e}"
        )


@router.get("/download-image")
async def download_image(
    url: str = Query(..., description="The image URL to download")
):
    """
    Proxy endpoint to download images from external sources (like Ideogram)
    and serve them directly to bypass CORS restrictions.
    """
    try:
        logger.info(f"📥 Proxying image download from: {url[:100]}...")

        # Validate that the URL is from a trusted source (Ideogram)
        if not url.startswith("https://ideogram.ai/"):
            raise HTTPException(
                status_code=400,
                detail="Only Ideogram image URLs are allowed"
            )

        logger.info(f"Attempting to fetch image from: {url}")

        # Fetch the image from the external URL with proper headers
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Referer': 'https://ideogram.ai/',
        }

        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(url, headers=headers)

            logger.info(f"External URL response status: {response.status_code}")

            if response.status_code != 200:
                logger.error(f"Failed to fetch image: {response.status_code}, Response: {response.text[:200]}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"Failed to fetch image from external source: {response.status_code}"
                )

            # Get content type from the response
            content_type = response.headers.get("content-type", "image/png")

            # Determine file extension based on content type
            if "jpeg" in content_type or "jpg" in content_type:
                file_extension = "jpg"
            elif "png" in content_type:
                file_extension = "png"
            elif "webp" in content_type:
                file_extension = "webp"
            else:
                file_extension = "png"  # Default fallback

            # Create filename with timestamp
            import time
            filename = f"imagen-{int(time.time())}.{file_extension}"

            # Return the image as a downloadable response
            return Response(
                content=response.content,
                media_type=content_type,
                headers={
                    "Content-Disposition": f"attachment; filename={filename}",
                    "Content-Length": str(len(response.content)),
                    "Cache-Control": "no-cache"
                }
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in download_image endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during image download: {e}"
        )


@router.get("/health")
async def health_check():
    """Health check endpoint for image generator service."""
    return {
        "status": "healthy",
        "service": "image-generator",
        "message": "Image Generator service is running"
    }
