import React, { useState } from "react";
import { motion } from "framer-motion";
import { useLocation, useRoute } from "wouter";
import {
  ArrowLeft,
  Play,
  Pause,
  Download,
  Settings,
  Upload,
  Type,
  Palette,
  Volume2,
  RotateCw,
  Zap,
  Save,
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Slider } from "@/components/ui/slider";
import { DashboardLayout } from "@/components/layout/dashboard-layout";

const videoTemplates = {
  spin: {
    name: "Spin",
    description: "Efecto de rotación 360° para productos",
    previewUrl: "/api/placeholder/400/300",
    settings: {
      rotationSpeed: 1,
      backgroundColor: "#ffffff",
      lighting: "professional",
    }
  },
  "model-turnaround": {
    name: "Model Turnaround",
    description: "Modelo girando para mostrar ropa y moda",
    previewUrl: "/api/placeholder/400/300",
    settings: {
      rotationSpeed: 0.5,
      backgroundColor: "#f8f9fa",
      lighting: "soft",
    }
  },
  orbit: {
    name: "Orbit",
    description: "Efecto orbital alrededor del producto",
    previewUrl: "/api/placeholder/400/300",
    settings: {
      orbitRadius: 50,
      backgroundColor: "#000000",
      lighting: "dramatic",
    }
  },
  "push-in": {
    name: "Push-in",
    description: "Efecto de zoom hacia el producto",
    previewUrl: "/api/placeholder/400/300",
    settings: {
      zoomLevel: 2,
      backgroundColor: "#f0f0f0",
      lighting: "natural",
    }
  },
  "ai-outfit": {
    name: "AI Outfit",
    description: "Cambio de outfits con IA generativa",
    previewUrl: "/api/placeholder/400/300",
    settings: {
      outfitStyle: "casual",
      backgroundColor: "#ffffff",
      lighting: "studio",
    }
  },
};

function VideoAdsEditorContent() {
  const [, navigate] = useLocation();
  const [match, params] = useRoute("/dashboard/ads-central/video-ads/editor/:templateId");
  const [isPlaying, setIsPlaying] = useState(false);
  const [productImage, setProductImage] = useState<string | null>(null);
  const [adText, setAdText] = useState("");
  const [settings, setSettings] = useState({
    duration: 5,
    quality: "HD",
    format: "MP4",
  });

  const templateId = params?.templateId || "spin";
  const template = videoTemplates[templateId as keyof typeof videoTemplates] || videoTemplates.spin;

  const handleProductUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setProductImage(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleGenerate = () => {
    // Aquí iría la lógica para generar el video
    console.log("Generating video with:", {
      template: templateId,
      productImage,
      adText,
      settings,
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center gap-4 mb-8"
        >
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate("/dashboard/ads-central/video-ads")}
            className="flex items-center gap-2 hover:bg-white/60 backdrop-blur-sm"
          >
            <ArrowLeft className="w-4 h-4" />
            Volver a Video Ads
          </Button>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <h1 className="text-3xl font-bold bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent mb-2">
            Editor de {template.name}
          </h1>
          <p className="text-lg text-slate-600">
            {template.description}
          </p>
        </motion.div>

        {/* Main Editor Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Preview Area */}
          <div className="lg:col-span-2">
            <Card className="h-fit">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Play className="w-5 h-5" />
                  Vista Previa
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="aspect-video bg-gradient-to-br from-slate-200 to-slate-300 rounded-lg flex items-center justify-center relative overflow-hidden">
                  {productImage ? (
                    <img
                      src={productImage}
                      alt="Product preview"
                      className="max-w-full max-h-full object-contain"
                    />
                  ) : (
                    <div className="text-center text-slate-500">
                      <Upload className="w-12 h-12 mx-auto mb-4" />
                      <p className="text-lg font-medium">Sube tu producto</p>
                      <p className="text-sm">para ver la vista previa</p>
                    </div>
                  )}
                  
                  {/* Play/Pause Overlay */}
                  <div className="absolute inset-0 bg-black/20 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                    <Button
                      size="lg"
                      variant="secondary"
                      className="rounded-full w-16 h-16 p-0"
                      onClick={() => setIsPlaying(!isPlaying)}
                    >
                      {isPlaying ? <Pause className="w-8 h-8" /> : <Play className="w-8 h-8" />}
                    </Button>
                  </div>
                </div>

                {/* Controls */}
                <div className="flex gap-2 mt-4">
                  <Button
                    onClick={() => setIsPlaying(!isPlaying)}
                    className="flex items-center gap-2"
                  >
                    {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                    {isPlaying ? "Pausar" : "Reproducir"}
                  </Button>
                  <Button variant="outline" className="flex items-center gap-2">
                    <Download className="w-4 h-4" />
                    Descargar
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Settings Panel */}
          <div className="space-y-6">
            {/* Product Upload */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Upload className="w-5 h-5" />
                  Producto
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="product-upload">Imagen del Producto</Label>
                  <Input
                    id="product-upload"
                    type="file"
                    accept="image/*"
                    onChange={handleProductUpload}
                    className="mt-2"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Text Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Type className="w-5 h-5" />
                  Texto del Anuncio
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="ad-text">Mensaje Principal</Label>
                  <Textarea
                    id="ad-text"
                    placeholder="Escribe el texto de tu anuncio..."
                    value={adText}
                    onChange={(e) => setAdText(e.target.value)}
                    rows={3}
                    className="mt-2"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Video Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="w-5 h-5" />
                  Configuración
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label>Duración (segundos)</Label>
                  <Slider
                    value={[settings.duration]}
                    onValueChange={(value) => setSettings({...settings, duration: value[0]})}
                    max={30}
                    min={3}
                    step={1}
                    className="mt-2"
                  />
                  <span className="text-sm text-slate-600">{settings.duration}s</span>
                </div>

                <div>
                  <Label htmlFor="quality">Calidad</Label>
                  <select
                    id="quality"
                    value={settings.quality}
                    onChange={(e) => setSettings({...settings, quality: e.target.value})}
                    className="w-full mt-2 p-2 border rounded-md"
                  >
                    <option value="HD">HD (720p)</option>
                    <option value="FHD">Full HD (1080p)</option>
                    <option value="4K">4K (2160p)</option>
                  </select>
                </div>
              </CardContent>
            </Card>

            {/* Generate Button */}
            <Button
              onClick={handleGenerate}
              className="w-full bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] hover:opacity-90 transition-opacity"
              size="lg"
            >
              <Zap className="w-5 h-5 mr-2" />
              Generar Video
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function VideoAdsEditorPage() {
  return (
    <DashboardLayout>
      <VideoAdsEditorContent />
    </DashboardLayout>
  );
}
