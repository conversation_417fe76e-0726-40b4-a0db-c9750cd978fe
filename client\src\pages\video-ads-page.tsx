import React, { useState } from "react";
import { motion } from "framer-motion";
import { useLocation } from "wouter";
import {
  Video,
  RotateCw,
  User,
  Circle,
  ZoomIn,
  Sparkles,
  ArrowLeft,
  Play,
  Download,
  Settings,
  Upload,
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { DashboardLayout } from "@/components/layout/dashboard-layout";

interface VideoTemplate {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  category: string;
  gradient: string;
  isPremium?: boolean;
  previewVideo?: string;
  features: string[];
}

const videoTemplates: VideoTemplate[] = [
  {
    id: "spin",
    title: "Spin",
    description: "Muestra un objeto girando en un fondo limpio. Ideal para mostrar productos individuales con un efecto 3D o rotatorio.",
    category: "Producto",
    icon: <RotateCw className="w-6 h-6" />,
    gradient: "from-blue-500 to-purple-600",
    features: ["Rotación 360°", "Fondo limpio", "Efecto 3D", "Ideal para joyería"],
  },
  {
    id: "model-turnaround",
    title: "Model Turnaround",
    description: "Presenta a una modelo girando o mostrándose de cuerpo completo. Útil para ropa, moda o estilo de vida.",
    category: "Moda",
    icon: <User className="w-6 h-6" />,
    gradient: "from-pink-500 to-rose-600",
    features: ["Modelo en movimiento", "Cuerpo completo", "Ideal para moda", "Estilo de vida"],
    isPremium: true,
  },
  {
    id: "orbit",
    title: "Orbit",
    description: "Imagen de un reloj con fondo desenfocado. Probablemente crea un efecto circular o de giro alrededor del producto.",
    category: "Lujo",
    icon: <Circle className="w-6 h-6" />,
    gradient: "from-amber-500 to-orange-600",
    features: ["Efecto orbital", "Fondo desenfocado", "Productos de lujo", "Movimiento circular"],
  },
  {
    id: "push-in",
    title: "Push-in",
    description: "Imagen de un coche deportivo en una casa moderna. Sugiere un efecto de zoom o 'push-in' hacia el producto.",
    category: "Automotriz",
    icon: <ZoomIn className="w-6 h-6" />,
    gradient: "from-emerald-500 to-teal-600",
    features: ["Efecto zoom", "Ambientes modernos", "Productos premium", "Acercamiento dinámico"],
  },
  {
    id: "ai-outfit",
    title: "AI Outfit",
    description: "Imagen de una mujer caminando como en pasarela. Puede generar o superponer outfits digitalmente sobre modelos.",
    category: "IA Fashion",
    icon: <Sparkles className="w-6 h-6" />,
    gradient: "from-violet-500 to-purple-600",
    features: ["IA generativa", "Cambio de outfits", "Pasarela virtual", "Moda digital"],
    isPremium: true,
  },
];

const categories = [
  { id: "all", name: "Todas", icon: <Video className="w-5 h-5" /> },
  { id: "Producto", name: "Producto", icon: <RotateCw className="w-5 h-5" /> },
  { id: "Moda", name: "Moda", icon: <User className="w-5 h-5" /> },
  { id: "Lujo", name: "Lujo", icon: <Circle className="w-5 h-5" /> },
  { id: "Automotriz", name: "Automotriz", icon: <ZoomIn className="w-5 h-5" /> },
  { id: "IA Fashion", name: "IA Fashion", icon: <Sparkles className="w-5 h-5" /> },
];

function VideoAdsContent() {
  const [, navigate] = useLocation();
  const [selectedCategory, setSelectedCategory] = useState("all");

  const filteredTemplates = selectedCategory === "all" 
    ? videoTemplates 
    : videoTemplates.filter(template => template.category === selectedCategory);

  const handleTemplateSelect = (template: VideoTemplate) => {
    // Navegar al editor de video ads con la plantilla seleccionada
    navigate(`/dashboard/ads-central/video-ads/editor/${template.id}`);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center gap-4 mb-8"
        >
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate("/dashboard/ads-central")}
            className="flex items-center gap-2 hover:bg-white/60 backdrop-blur-sm"
          >
            <ArrowLeft className="w-4 h-4" />
            Volver a Ads Central
          </Button>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-bold bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent mb-4">
            🎬 Video Ads
          </h1>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto">
            Crea anuncios de video profesionales con plantillas prefabricadas. 
            Elige una plantilla visual y personaliza tu contenido comercial.
          </p>
        </motion.div>

        {/* Categories Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="flex flex-wrap gap-2 justify-center mb-8"
        >
          {categories.map((category) => (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory(category.id)}
              className={`flex items-center gap-2 ${
                selectedCategory === category.id
                  ? "bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white"
                  : "hover:bg-white/60 backdrop-blur-sm"
              }`}
            >
              {category.icon}
              {category.name}
            </Button>
          ))}
        </motion.div>

        {/* Templates Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        >
          {filteredTemplates.map((template, index) => (
            <motion.div
              key={template.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ scale: 1.02 }}
              className="group"
            >
              <Card className="h-full overflow-hidden hover:shadow-xl transition-all duration-300 backdrop-blur-sm bg-white/80 border border-white/20">
                <div className={`h-48 bg-gradient-to-br ${template.gradient} relative overflow-hidden`}>
                  <div className="absolute inset-0 bg-black/20" />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-white text-6xl opacity-80">
                      {template.icon}
                    </div>
                  </div>
                  {template.isPremium && (
                    <Badge className="absolute top-3 right-3 bg-gradient-to-r from-amber-400 to-orange-500 text-white border-0">
                      Premium
                    </Badge>
                  )}
                  <div className="absolute bottom-3 left-3 right-3">
                    <Badge variant="secondary" className="bg-white/20 text-white border-0 backdrop-blur-sm">
                      {template.category}
                    </Badge>
                  </div>
                </div>

                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center gap-2 text-lg">
                    {template.title}
                  </CardTitle>
                  <CardDescription className="text-sm">
                    {template.description}
                  </CardDescription>
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* Features */}
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-slate-700">Características:</h4>
                    <div className="flex flex-wrap gap-1">
                      {template.features.map((feature, idx) => (
                        <Badge key={idx} variant="outline" className="text-xs">
                          {feature}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2 pt-2">
                    <Button
                      onClick={() => handleTemplateSelect(template)}
                      className="flex-1 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] hover:opacity-90 transition-opacity"
                    >
                      <Play className="w-4 h-4 mr-2" />
                      Usar Plantilla
                    </Button>
                    <Button variant="outline" size="sm" className="hover:bg-white/60">
                      <Settings className="w-4 h-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>


      </div>
    </div>
  );
}

export default function VideoAdsPage() {
  return (
    <DashboardLayout>
      <VideoAdsContent />
    </DashboardLayout>
  );
}
