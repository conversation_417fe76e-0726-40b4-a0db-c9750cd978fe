"use client"

import React, { useEffect, useState } from "react"
import { <PERSON> } from "wouter"
import { LucideIcon, Users, Briefcase, CreditCard, Calculator, BookOpen } from "lucide-react"
import { cn } from "@/lib/utils"
import { emma<PERSON>i<PERSON><PERSON> } from "@/assets"

interface NavItem {
  name: string
  url: string
  icon: LucideIcon
}

interface NavBarProps {
  items: NavItem[]
  className?: string
}

export function EmmaNavBar({ items, className }: NavBarProps) {
  const [activeTab, setActiveTab] = useState(items[0].name)
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768)
    }

    handleResize()
    window.addEventListener("resize", handleResize)
    return () => window.removeEventListener("resize", handleResize)
  }, [])

  return (
    <div
      className={cn(
        "fixed bottom-0 sm:top-0 left-1/2 -translate-x-1/2 z-50 mb-6 sm:pt-12 pointer-events-none",
        className,
      )}
    >
      <div className="flex items-center gap-3 bg-white/10 border border-white/20 backdrop-blur-lg py-3 px-3 rounded-full shadow-lg pointer-events-auto">
        {items.map((item) => {
          const Icon = item.icon
          const isActive = activeTab === item.name

          return (
            <Link
              key={item.name}
              href={item.url}
              onClick={() => setActiveTab(item.name)}
              className={cn(
                "relative cursor-pointer text-sm font-semibold px-6 py-3 rounded-full transition-all duration-300 ease-out",
                "text-gray-700 hover:text-[#3018ef]",
                isActive && item.name !== "Emma" && "bg-white/20 text-[#3018ef] shadow-md",
                item.name === "Emma" && "px-4" // Menos padding horizontal para el logo
              )}
            >
              {item.name === "Emma" ? (
                <div className="flex items-center justify-center">
                  <img
                    src={emmaAiLogo}
                    alt="Emma Logo"
                    className="w-16 h-16 object-contain"
                  />
                </div>
              ) : (
                <>
                  <span className="hidden md:inline">{item.name}</span>
                  <span className="md:hidden">
                    <Icon size={18} strokeWidth={2.5} />
                  </span>
                </>
              )}
              {isActive && item.name !== "Emma" && (
                <div
                  className="absolute inset-0 w-full bg-[#3018ef]/10 rounded-full -z-10 transition-all duration-300"
                >
                  <div className="absolute -top-2 left-1/2 -translate-x-1/2 w-8 h-1 bg-[#3018ef] rounded-t-full">
                    <div className="absolute w-12 h-6 bg-[#3018ef]/20 rounded-full blur-md -top-2 -left-2" />
                    <div className="absolute w-8 h-6 bg-[#3018ef]/20 rounded-full blur-md -top-1" />
                    <div className="absolute w-4 h-4 bg-[#3018ef]/20 rounded-full blur-sm top-0 left-2" />
                  </div>
                </div>
              )}
            </Link>
          )
        })}
      </div>
    </div>
  )
}

// Emma Navigation Items
export const emmaNavItems: NavItem[] = [
  { name: "Emma", url: "/", icon: Users }, // Redirect to new landing page
  { name: "Profesionales IA", url: "/profesionales-ia", icon: Users },
  { name: "Soluciones", url: "/soluciones-negocio", icon: Briefcase },
  { name: "Blog", url: "/blog", icon: BookOpen },
  { name: "Planes", url: "/login", icon: CreditCard },
  { name: "Calculadora", url: "/calculadora", icon: Calculator },
]
