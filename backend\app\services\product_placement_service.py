"""
Product Placement Service - Professional product mockup generator
Generates realistic background scenes and composites products for marketing materials.
Integrates with Ideogram AI for high-quality background generation.
"""

import logging
import httpx
import os
import base64
import io
from typing import Dict, Any, Optional, List
from fastapi import UploadFile
from PIL import Image, ImageEnhance, ImageFilter
from app.core.config import settings
from app.services.product_placement.ideogram_background_service import ideogram_background_service

logger = logging.getLogger(__name__)


class ProductPlacementService:
    """Service for generating product placement mockups using Ideogram AI backgrounds."""
    
    def __init__(self):
        """Initialize the Product Placement service."""
        self.supported_formats = ["PNG", "JPEG", "JPG", "WEBP"]
        
    async def generate_background_scene(
        self,
        scene_prompt: str,
        style: str = "realistic",
        size: str = "1024x1024",
        product_image_data: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Generate a background scene using Ideogram AI optimized for product placement.
        
        Args:
            scene_prompt: Description of the background scene (e.g., "modern kitchen", "outdoor plaza")
            style: Style of the background (realistic, minimalist, lifestyle, commercial)
            size: Image dimensions
            
        Returns:
            Dict with success status, background image data, and metadata
        """
        try:
            logger.info(f"🎨 [PRODUCT PLACEMENT] Starting background generation for: {scene_prompt}")
            logger.info(f"📐 [PRODUCT PLACEMENT] Style: {style}, Size: {size}")
            logger.info(f"🔍 [PRODUCT PLACEMENT] Product image provided: {bool(product_image_data)}")

            # The specialized service handles all prompt enhancement and configuration

            # Use specialized Product Placement Ideogram service
            logger.info(f"🔄 Calling specialized Product Placement Ideogram service...")
            if product_image_data:
                logger.info(f"🖼️ Using product image as style reference for better background matching")

            result = await ideogram_background_service.generate_background_options(
                scene_prompt=scene_prompt,
                style=style,
                size=size,
                num_options=4,  # Generate 4 options for selection
                product_image_data=product_image_data  # Pass product image as reference
            )

            logger.info(f"📥 Received result from Ideogram: success={result.get('success', False)}")

            if result["success"]:
                # Add product placement metadata while preserving all fields
                if "metadata" in result:
                    result["metadata"]["type"] = "product_placement_background"
                    result["metadata"]["scene_type"] = scene_prompt
                    result["metadata"]["style"] = style
                    result["metadata"]["optimized_for"] = "product_placement"

                # Log the number of images generated
                total_images = result.get("metadata", {}).get("total_images", 1)
                logger.info(f"✅ Generated {total_images} product placement background options: {scene_prompt}")

                # Ensure all fields are preserved (images, image_url, revised_prompt, etc.)
                return result
            else:
                logger.error(f"❌ Failed to generate background: {result.get('error', 'Unknown error')}")
                return result
                
        except Exception as e:
            logger.error(f"Error generating background scene: {e}")
            return {"success": False, "error": f"Error generating background: {str(e)}"}
    
    async def process_product_image(self, product_file: UploadFile) -> Dict[str, Any]:
        """
        Process and optimize a product image for placement.
        
        Args:
            product_file: Uploaded product image file
            
        Returns:
            Dict with success status, processed image data, and metadata
        """
        try:
            # Validate file format
            if not product_file.content_type or not any(
                fmt.lower() in product_file.content_type.lower() 
                for fmt in ["image/png", "image/jpeg", "image/jpg", "image/webp"]
            ):
                return {
                    "success": False, 
                    "error": "Formato de imagen no soportado. Use PNG, JPEG o WEBP."
                }
            
            # Read and process image
            image_data = await product_file.read()
            image = Image.open(io.BytesIO(image_data))
            
            # Convert to RGBA for transparency support
            if image.mode != 'RGBA':
                image = image.convert('RGBA')
            
            # Get original dimensions
            original_width, original_height = image.size
            
            # Optimize image size (max 1024px on longest side)
            max_size = 1024
            if max(original_width, original_height) > max_size:
                ratio = max_size / max(original_width, original_height)
                new_width = int(original_width * ratio)
                new_height = int(original_height * ratio)
                image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # Enhance image quality
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(1.1)  # Slight sharpening
            
            # Convert to base64 for frontend
            buffer = io.BytesIO()
            image.save(buffer, format='PNG', optimize=True)
            image_base64 = base64.b64encode(buffer.getvalue()).decode()
            
            return {
                "success": True,
                "image_data": f"data:image/png;base64,{image_base64}",
                "metadata": {
                    "original_filename": product_file.filename,
                    "original_size": {"width": original_width, "height": original_height},
                    "processed_size": {"width": image.width, "height": image.height},
                    "format": "PNG",
                    "has_transparency": True,
                    "file_size": len(image_data)
                }
            }
            
        except Exception as e:
            logger.error(f"Error processing product image: {e}")
            return {"success": False, "error": f"Error procesando imagen: {str(e)}"}


    async def composite_product_on_background(
        self,
        background_url: str,
        product_image_data: str,
        position: Dict[str, float],
        scale: float = 1.0,
        rotation: float = 0.0,
        shadow_intensity: float = 0.3
    ) -> Dict[str, Any]:
        """
        Composite a product image onto a background with positioning and effects.

        Args:
            background_url: URL of the background image
            product_image_data: Base64 encoded product image
            position: {"x": 0.5, "y": 0.5} - relative position (0-1)
            scale: Scale factor for the product (0.1-2.0)
            rotation: Rotation angle in degrees
            shadow_intensity: Shadow effect intensity (0-1)

        Returns:
            Dict with success status, composite image data, and metadata
        """
        try:
            # Download background image
            async with httpx.AsyncClient(timeout=30.0) as client:
                bg_response = await client.get(background_url)
                if bg_response.status_code != 200:
                    return {"success": False, "error": "No se pudo descargar la imagen de fondo"}

                background = Image.open(io.BytesIO(bg_response.content))

            # Process product image from base64
            if product_image_data.startswith('data:image'):
                product_image_data = product_image_data.split(',')[1]

            product_data = base64.b64decode(product_image_data)
            product = Image.open(io.BytesIO(product_data))

            # Ensure both images are in RGBA mode
            if background.mode != 'RGBA':
                background = background.convert('RGBA')
            if product.mode != 'RGBA':
                product = product.convert('RGBA')

            # Scale product image
            if scale != 1.0:
                new_width = int(product.width * scale)
                new_height = int(product.height * scale)
                product = product.resize((new_width, new_height), Image.Resampling.LANCZOS)

            # Rotate product image
            if rotation != 0.0:
                product = product.rotate(rotation, expand=True, fillcolor=(0, 0, 0, 0))

            # Calculate position on background
            bg_width, bg_height = background.size
            prod_width, prod_height = product.size

            x = int((bg_width - prod_width) * position["x"])
            y = int((bg_height - prod_height) * position["y"])

            # Create shadow if requested
            composite = background.copy()
            if shadow_intensity > 0:
                shadow = self._create_product_shadow(product, shadow_intensity)
                shadow_offset = 5
                composite.paste(shadow, (x + shadow_offset, y + shadow_offset), shadow)

            # Composite product onto background
            composite.paste(product, (x, y), product)

            # Convert to base64
            buffer = io.BytesIO()
            composite.save(buffer, format='PNG', optimize=True)
            composite_base64 = base64.b64encode(buffer.getvalue()).decode()

            return {
                "success": True,
                "image_data": f"data:image/png;base64,{composite_base64}",
                "metadata": {
                    "background_size": {"width": bg_width, "height": bg_height},
                    "product_size": {"width": prod_width, "height": prod_height},
                    "position": position,
                    "scale": scale,
                    "rotation": rotation,
                    "shadow_intensity": shadow_intensity,
                    "composite_size": {"width": composite.width, "height": composite.height}
                }
            }

        except Exception as e:
            logger.error(f"Error compositing product on background: {e}")
            return {"success": False, "error": f"Error creando composición: {str(e)}"}

    def _create_product_shadow(self, product: Image.Image, intensity: float) -> Image.Image:
        """Create a realistic shadow for the product."""
        # Create shadow from product alpha channel
        shadow = Image.new('RGBA', product.size, (0, 0, 0, 0))

        # Extract alpha channel and create shadow
        alpha = product.split()[-1]
        shadow_alpha = alpha.point(lambda x: int(x * intensity) if x > 0 else 0)

        # Create black shadow with calculated alpha
        shadow_color = Image.new('RGB', product.size, (0, 0, 0))
        shadow.paste(shadow_color, (0, 0))
        shadow.putalpha(shadow_alpha)

        # Blur the shadow for realism
        shadow = shadow.filter(ImageFilter.GaussianBlur(radius=3))

        return shadow

    async def get_scene_suggestions(self) -> List[Dict[str, str]]:
        """Get predefined scene suggestions for product placement."""
        return [
            {
                "id": "modern_kitchen",
                "name": "Cocina Moderna",
                "prompt": "modern kitchen with marble countertops and natural lighting",
                "category": "interior"
            },
            {
                "id": "outdoor_plaza",
                "name": "Plaza Exterior",
                "prompt": "outdoor plaza with stone pavement and urban architecture",
                "category": "exterior"
            },
            {
                "id": "coffee_shop",
                "name": "Cafetería",
                "prompt": "cozy coffee shop interior with wooden tables and warm lighting",
                "category": "commercial"
            },
            {
                "id": "office_desk",
                "name": "Escritorio de Oficina",
                "prompt": "clean modern office desk with natural window lighting",
                "category": "workspace"
            },
            {
                "id": "living_room",
                "name": "Sala de Estar",
                "prompt": "modern living room with comfortable furniture and natural light",
                "category": "interior"
            },
            {
                "id": "retail_store",
                "name": "Tienda Retail",
                "prompt": "modern retail store interior with clean displays and professional lighting",
                "category": "commercial"
            },
            {
                "id": "outdoor_park",
                "name": "Parque Natural",
                "prompt": "beautiful park setting with green grass and natural scenery",
                "category": "nature"
            },
            {
                "id": "studio_setup",
                "name": "Estudio Fotográfico",
                "prompt": "professional photography studio with clean white background and perfect lighting",
                "category": "studio"
            }
        ]


# Create service instance
product_placement_service = ProductPlacementService()
