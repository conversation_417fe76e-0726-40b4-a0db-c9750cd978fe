"""
Real SEO Ecommerce Service for Emma Studio
Implements Google's official SEO methodology with real data sources
No simulated or fake data - only authentic analysis
"""

import asyncio
import aiohttp
import json
import re
from typing import Dict, List, Optional, Any
from urllib.parse import urlparse, urljoin
from bs4 import BeautifulSoup
import logging
from dataclasses import dataclass
from datetime import datetime

from ..core.real_seo_config import get_real_seo_settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class RealProductData:
    """Real product data structure following Google's ecommerce schema"""
    title: str
    description: str
    price: Optional[float]
    currency: str
    category: str
    brand: str
    sku: Optional[str]
    availability: str
    url: str
    image_url: Optional[str]
    rating: Optional[float]
    review_count: Optional[int]
    structured_data: Dict[str, Any]
    seo_metrics: Dict[str, Any]

@dataclass
class SEOEcommerceAnalysis:
    """Complete SEO analysis following Google's methodology"""
    brand_name: str
    total_products: int
    real_products: List[RealProductData]
    seo_keywords: List[Dict[str, Any]]
    technical_seo: Dict[str, Any]
    content_analysis: Dict[str, Any]
    performance_metrics: Dict[str, Any]
    recommendations: List[str]
    analysis_timestamp: datetime
    data_sources: List[str]

class RealSEOEcommerceService:
    """
    Real SEO Ecommerce Service using authentic data sources
    Follows Google's official SEO methodology for ecommerce
    """
    
    def __init__(self):
        # Load configuration
        self.settings = get_real_seo_settings()

        # Real API keys from configuration
        self.google_api_key = self.settings.GOOGLE_API_KEY
        self.serper_api_key = self.settings.SERPER_API_KEY
        self.google_cse_id = self.settings.GOOGLE_CSE_ID

        # Session for HTTP requests
        self.session = None

        # Validate configuration
        if not self.google_api_key:
            logger.warning("⚠️ Google API key not configured")
        if not self.serper_api_key:
            logger.warning("⚠️ Serper API key not configured")
        if not self.google_cse_id:
            logger.warning("⚠️ Google Custom Search Engine ID not configured")
        
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def analyze_ecommerce_brand(self, brand_input: str) -> SEOEcommerceAnalysis:
        """
        Perform comprehensive SEO analysis of an ecommerce brand
        
        Args:
            brand_input: Brand name or website URL
            
        Returns:
            Complete SEO analysis with real data
        """
        logger.info(f"🔍 Starting REAL SEO analysis for: {brand_input}")
        
        # Determine if input is URL or brand name
        is_url = self._is_url(brand_input)
        
        if is_url:
            return await self._analyze_website_seo(brand_input)
        else:
            return await self._analyze_brand_seo(brand_input)
    
    def _is_url(self, text: str) -> bool:
        """Check if text is a valid URL"""
        try:
            result = urlparse(text)
            return all([result.scheme, result.netloc])
        except:
            return False
    
    async def _analyze_website_seo(self, url: str) -> SEOEcommerceAnalysis:
        """
        Analyze a specific website for SEO ecommerce optimization
        Uses real web scraping and API data
        """
        logger.info(f"🌐 Analyzing website: {url}")
        
        try:
            # Step 1: Extract real products from website
            real_products = await self._extract_real_products_from_site(url)
            
            # Step 2: Analyze technical SEO
            technical_seo = await self._analyze_technical_seo(url)
            
            # Step 3: Extract brand information
            brand_name = await self._extract_brand_name(url)
            
            # Step 4: Generate real SEO keywords using improved methods
            seo_keywords = await self._generate_real_seo_keywords_improved(brand_name, real_products)
            
            # Step 5: Analyze content and structure
            content_analysis = await self._analyze_content_structure(url)
            
            # Step 6: Get performance metrics
            performance_metrics = await self._get_performance_metrics(url)
            
            # Step 7: Generate actionable recommendations
            recommendations = self._generate_seo_recommendations(
                technical_seo, content_analysis, performance_metrics
            )
            
            return SEOEcommerceAnalysis(
                brand_name=brand_name,
                total_products=len(real_products),
                real_products=real_products,
                seo_keywords=seo_keywords,
                technical_seo=technical_seo,
                content_analysis=content_analysis,
                performance_metrics=performance_metrics,
                recommendations=recommendations,
                analysis_timestamp=datetime.now(),
                data_sources=["website_scraping", "serper_api", "technical_analysis"]
            )
            
        except Exception as e:
            logger.error(f"❌ Error analyzing website {url}: {e}")
            raise
    
    async def _analyze_brand_seo(self, brand_name: str) -> SEOEcommerceAnalysis:
        """
        Analyze a brand using improved search and real data sources
        """
        logger.info(f"🏷️ Analyzing brand: {brand_name}")

        try:
            # Step 1: Find real brand websites using multiple strategies
            brand_websites = await self._find_brand_websites_improved(brand_name)

            # Step 2: Extract products from found websites
            all_products = []
            data_sources = []

            for website in brand_websites[:5]:  # Analyze top 5 websites
                logger.info(f"🌐 Analyzing website: {website}")
                products = await self._extract_real_products_from_site(website)
                if products:
                    all_products.extend(products)
                    data_sources.append(f"website_extraction_{urlparse(website).netloc}")

            # Step 3: Generate comprehensive SEO analysis
            seo_keywords = await self._generate_real_seo_keywords_improved(brand_name, all_products)

            # Step 4: Analyze brand presence and competition
            brand_analysis = await self._analyze_brand_presence(brand_name)

            # Add data sources
            if not data_sources:
                data_sources = ["direct_search", "brand_analysis"]

            return SEOEcommerceAnalysis(
                brand_name=brand_name,
                total_products=len(all_products),
                real_products=all_products,
                seo_keywords=seo_keywords,
                technical_seo=brand_analysis.get("technical_seo", {}),
                content_analysis=brand_analysis.get("content_analysis", {}),
                performance_metrics=brand_analysis.get("performance_metrics", {}),
                recommendations=brand_analysis.get("recommendations", []),
                analysis_timestamp=datetime.now(),
                data_sources=data_sources
            )

        except Exception as e:
            logger.error(f"❌ Error analyzing brand {brand_name}: {e}")
            raise
    
    async def _extract_real_products_from_site(self, url: str) -> List[RealProductData]:
        """
        Extract real products from a website using multiple strategies with improved handling
        """
        logger.info(f"📦 Extracting real products from: {url}")

        if not self.session:
            raise RuntimeError("Service not initialized. Use async context manager.")

        try:
            # Use realistic headers to avoid blocking
            headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }

            async with self.session.get(url, headers=headers, timeout=20) as response:
                logger.info(f"🌐 Response status: {response.status} for {url}")

                if response.status != 200:
                    logger.warning(f"⚠️ HTTP {response.status} for {url}")
                    return []

                html_content = await response.text()
                soup = BeautifulSoup(html_content, 'html.parser')

                products = []

                # Strategy 1: Extract from JSON-LD structured data (PRIORITY)
                logger.info("🔍 Strategy 1: Extracting from JSON-LD...")
                json_ld_products = self._extract_from_json_ld(soup, url)
                products.extend(json_ld_products)
                logger.info(f"   Found {len(json_ld_products)} products from JSON-LD")

                # Strategy 2: Extract from microdata
                logger.info("🔍 Strategy 2: Extracting from microdata...")
                microdata_products = self._extract_from_microdata(soup, url)
                products.extend(microdata_products)
                logger.info(f"   Found {len(microdata_products)} products from microdata")

                # Strategy 3: Extract from Open Graph and meta tags
                logger.info("🔍 Strategy 3: Extracting from meta tags...")
                meta_products = self._extract_from_meta_tags(soup, url)
                products.extend(meta_products)
                logger.info(f"   Found {len(meta_products)} products from meta tags")

                # Strategy 4: Extract from common ecommerce patterns
                logger.info("🔍 Strategy 4: Extracting from ecommerce patterns...")
                pattern_products = self._extract_from_ecommerce_patterns(soup, url)
                products.extend(pattern_products)
                logger.info(f"   Found {len(pattern_products)} products from patterns")

                # Remove duplicates and validate
                unique_products = self._deduplicate_products(products)

                logger.info(f"✅ Total extracted: {len(products)} products, unique: {len(unique_products)}")

                # Return more products for better analysis
                return unique_products[:50]  # Increased limit

        except Exception as e:
            logger.error(f"❌ Error extracting products from {url}: {e}")
            return []
    
    def _extract_from_json_ld(self, soup: BeautifulSoup, base_url: str) -> List[RealProductData]:
        """Extract products from JSON-LD structured data"""
        products = []
        
        # Find all JSON-LD scripts
        json_scripts = soup.find_all('script', type='application/ld+json')
        
        for script in json_scripts:
            try:
                data = json.loads(script.string)
                
                # Handle different JSON-LD structures
                if isinstance(data, list):
                    for item in data:
                        product = self._parse_json_ld_product(item, base_url)
                        if product:
                            products.append(product)
                else:
                    product = self._parse_json_ld_product(data, base_url)
                    if product:
                        products.append(product)
                        
            except (json.JSONDecodeError, Exception) as e:
                logger.debug(f"Error parsing JSON-LD: {e}")
                continue
        
        return products
    
    def _parse_json_ld_product(self, data: Dict, base_url: str) -> Optional[RealProductData]:
        """Parse a single JSON-LD product object with improved extraction"""
        try:
            # Check if this is a Product schema (more flexible matching)
            schema_type = str(data.get('@type', '')).lower()
            if 'product' not in schema_type:
                return None

            logger.info(f"🔍 Found Product schema: {schema_type}")

            # Extract product information
            title = data.get('name', '')
            description = data.get('description', '')

            # Extract price information with better handling
            offers = data.get('offers', {})
            if isinstance(offers, list) and offers:
                offers = offers[0]

            price = None
            currency = 'USD'
            availability = 'unknown'

            if offers:
                # Try multiple price fields
                price_str = (
                    offers.get('price') or
                    offers.get('lowPrice') or
                    offers.get('highPrice') or
                    offers.get('priceSpecification', {}).get('price', '')
                )

                if price_str:
                    try:
                        # Clean and convert price
                        clean_price = re.sub(r'[^\d.]', '', str(price_str))
                        if clean_price:
                            price = float(clean_price)
                            logger.info(f"💰 Extracted price: ${price}")
                    except Exception as e:
                        logger.debug(f"Price parsing error: {e}")

                currency = offers.get('priceCurrency', 'USD')
                availability = offers.get('availability', 'unknown')

                # Clean availability URL if present
                if 'http' in availability:
                    availability = availability.split('/')[-1]

            # Extract brand information with better handling
            brand = data.get('brand', {})
            if isinstance(brand, dict):
                brand = brand.get('name', brand.get('@id', ''))
            elif isinstance(brand, str):
                brand = brand
            else:
                brand = ''

            # Extract category with fallbacks
            category = (
                data.get('category') or
                data.get('productCategory') or
                data.get('additionalType', '')
            )

            # Extract SKU/ID with multiple fallbacks
            sku = (
                data.get('sku') or
                data.get('productID') or
                data.get('gtin') or
                data.get('mpn') or
                data.get('@id', '')
            )

            # Extract image with better handling
            image_url = data.get('image', '')
            if isinstance(image_url, list) and image_url:
                # Get first image or find best quality
                image_url = image_url[0]
                if isinstance(image_url, dict):
                    image_url = image_url.get('url', image_url.get('@id', ''))
            elif isinstance(image_url, dict):
                image_url = image_url.get('url', image_url.get('@id', ''))

            # Extract rating information
            rating = None
            review_count = None
            aggregate_rating = data.get('aggregateRating', {})
            if aggregate_rating:
                rating = aggregate_rating.get('ratingValue')
                review_count = aggregate_rating.get('reviewCount', aggregate_rating.get('ratingCount'))

                # Convert to numbers if they're strings
                try:
                    if rating:
                        rating = float(rating)
                    if review_count:
                        review_count = int(review_count)
                except:
                    pass

            # Validate minimum required data
            if not title or len(title.strip()) < 3:
                logger.debug(f"Skipping product with invalid title: '{title}'")
                return None

            logger.info(f"✅ Successfully parsed product: {title}")

            return RealProductData(
                title=title.strip(),
                description=description.strip() if description else '',
                price=price,
                currency=currency,
                category=category.strip() if category else '',
                brand=str(brand).strip() if brand else '',
                sku=str(sku).strip() if sku else '',
                availability=availability,
                url=base_url,
                image_url=image_url.strip() if image_url else '',
                rating=rating,
                review_count=review_count,
                structured_data=data,
                seo_metrics={}
            )

        except Exception as e:
            logger.error(f"❌ Error parsing JSON-LD product: {e}")
            return None

    def _extract_from_microdata(self, soup: BeautifulSoup, base_url: str) -> List[RealProductData]:
        """Extract products from microdata markup"""
        products = []

        # Find elements with product microdata
        product_elements = soup.find_all(attrs={"itemtype": re.compile(r".*Product.*", re.I)})

        for element in product_elements:
            try:
                title = ""
                description = ""
                price = None
                currency = "USD"
                brand = ""
                category = ""

                # Extract title
                name_elem = element.find(attrs={"itemprop": "name"})
                if name_elem:
                    title = name_elem.get_text(strip=True)

                # Extract description
                desc_elem = element.find(attrs={"itemprop": "description"})
                if desc_elem:
                    description = desc_elem.get_text(strip=True)

                # Extract price
                price_elem = element.find(attrs={"itemprop": "price"})
                if price_elem:
                    price_text = price_elem.get("content") or price_elem.get_text(strip=True)
                    try:
                        price = float(re.sub(r'[^\d.]', '', price_text))
                    except:
                        pass

                # Extract brand
                brand_elem = element.find(attrs={"itemprop": "brand"})
                if brand_elem:
                    brand = brand_elem.get_text(strip=True)

                if title:
                    products.append(RealProductData(
                        title=title,
                        description=description,
                        price=price,
                        currency=currency,
                        category=category,
                        brand=brand,
                        sku="",
                        availability="unknown",
                        url=base_url,
                        image_url="",
                        rating=None,
                        review_count=None,
                        structured_data={},
                        seo_metrics={}
                    ))

            except Exception as e:
                logger.debug(f"Error parsing microdata product: {e}")
                continue

        return products

    def _extract_from_meta_tags(self, soup: BeautifulSoup, base_url: str) -> List[RealProductData]:
        """Extract product info from meta tags and Open Graph"""
        products = []

        try:
            # Extract from Open Graph tags
            og_title = soup.find("meta", property="og:title")
            og_description = soup.find("meta", property="og:description")
            og_image = soup.find("meta", property="og:image")
            og_type = soup.find("meta", property="og:type")

            # Check if this is a product page
            if og_type and "product" in og_type.get("content", "").lower():
                title = og_title.get("content", "") if og_title else ""
                description = og_description.get("content", "") if og_description else ""
                image_url = og_image.get("content", "") if og_image else ""

                if title:
                    products.append(RealProductData(
                        title=title,
                        description=description,
                        price=None,
                        currency="USD",
                        category="",
                        brand="",
                        sku="",
                        availability="unknown",
                        url=base_url,
                        image_url=image_url,
                        rating=None,
                        review_count=None,
                        structured_data={},
                        seo_metrics={}
                    ))

        except Exception as e:
            logger.debug(f"Error extracting from meta tags: {e}")

        return products

    def _extract_from_ecommerce_patterns(self, soup: BeautifulSoup, base_url: str) -> List[RealProductData]:
        """Extract products using common ecommerce HTML patterns"""
        products = []

        # Common product selectors
        product_selectors = [
            ".product", ".product-item", ".product-card",
            "[data-product]", ".item", ".listing-item",
            ".product-tile", ".product-box"
        ]

        for selector in product_selectors:
            elements = soup.select(selector)

            for element in elements[:10]:  # Limit to 10 per selector
                try:
                    # Extract title
                    title = ""
                    title_selectors = ["h1", "h2", "h3", ".title", ".name", ".product-name"]
                    for title_sel in title_selectors:
                        title_elem = element.select_one(title_sel)
                        if title_elem:
                            title = title_elem.get_text(strip=True)
                            break

                    # Extract price
                    price = None
                    price_selectors = [".price", ".cost", ".amount", "[data-price]"]
                    for price_sel in price_selectors:
                        price_elem = element.select_one(price_sel)
                        if price_elem:
                            price_text = price_elem.get_text(strip=True)
                            try:
                                price = float(re.sub(r'[^\d.]', '', price_text))
                                break
                            except:
                                continue

                    # Extract description
                    description = ""
                    desc_selectors = [".description", ".summary", ".excerpt"]
                    for desc_sel in desc_selectors:
                        desc_elem = element.select_one(desc_sel)
                        if desc_elem:
                            description = desc_elem.get_text(strip=True)
                            break

                    if title and len(title) > 3:
                        products.append(RealProductData(
                            title=title,
                            description=description,
                            price=price,
                            currency="USD",
                            category="",
                            brand="",
                            sku="",
                            availability="unknown",
                            url=base_url,
                            image_url="",
                            rating=None,
                            review_count=None,
                            structured_data={},
                            seo_metrics={}
                        ))

                except Exception as e:
                    logger.debug(f"Error extracting product pattern: {e}")
                    continue

        return products

    def _deduplicate_products(self, products: List[RealProductData]) -> List[RealProductData]:
        """Remove duplicate products based on title similarity"""
        if not products:
            return []

        unique_products = []
        seen_titles = set()

        for product in products:
            # Normalize title for comparison
            normalized_title = re.sub(r'[^\w\s]', '', product.title.lower()).strip()

            if normalized_title not in seen_titles and len(normalized_title) > 3:
                seen_titles.add(normalized_title)
                unique_products.append(product)

        return unique_products

    async def _generate_real_seo_keywords_improved(self, brand_name: str, products: List[RealProductData]) -> List[Dict[str, Any]]:
        """Generate real SEO keywords using multiple strategies"""
        logger.info(f"🔑 Generating real SEO keywords for {brand_name}")

        keywords = []

        try:
            # Strategy 1: Extract keywords from actual product data
            if products:
                keywords.extend(self._extract_keywords_from_products(products, brand_name))

            # Strategy 2: Generate brand-based keywords
            keywords.extend(self._generate_brand_keywords(brand_name))

            # Strategy 3: Try Serper API if available (fallback)
            if self.serper_api_key:
                try:
                    serper_keywords = await self._generate_serper_keywords(brand_name, products)
                    keywords.extend(serper_keywords)
                except:
                    logger.warning("⚠️ Serper API not available, using alternative methods")

            # Strategy 4: Use Google Custom Search if available
            if self.google_cse_id:
                google_keywords = await self._generate_google_keywords(brand_name)
                keywords.extend(google_keywords)

            # Remove duplicates and sort by relevance
            unique_keywords = self._deduplicate_keywords(keywords)

            logger.info(f"✅ Generated {len(unique_keywords)} real SEO keywords")
            return unique_keywords[:50]  # Return top 50 keywords

        except Exception as e:
            logger.error(f"❌ Error generating SEO keywords: {e}")
            return []

    def _extract_keywords_from_products(self, products: List[RealProductData], brand_name: str) -> List[Dict[str, Any]]:
        """Extract keywords from actual product data"""
        keywords = []

        for product in products:
            # Extract from product title
            title_words = re.findall(r'\b\w{4,}\b', product.title.lower())
            for word in title_words:
                if word not in ['this', 'that', 'with', 'from', 'they', 'have', 'been', 'will']:
                    keywords.append({
                        'keyword': word,
                        'relevance_score': 0.9,
                        'source': 'product_title',
                        'search_volume': 'unknown',
                        'competition': 'unknown'
                    })

            # Extract from category
            if product.category:
                category_words = re.findall(r'\b\w{4,}\b', product.category.lower())
                for word in category_words:
                    keywords.append({
                        'keyword': word,
                        'relevance_score': 0.8,
                        'source': 'product_category',
                        'search_volume': 'unknown',
                        'competition': 'unknown'
                    })

            # Extract from description
            if product.description:
                desc_words = re.findall(r'\b\w{4,}\b', product.description.lower())
                for word in desc_words[:5]:  # Top 5 words from description
                    keywords.append({
                        'keyword': word,
                        'relevance_score': 0.6,
                        'source': 'product_description',
                        'search_volume': 'unknown',
                        'competition': 'unknown'
                    })

        return keywords

    def _generate_brand_keywords(self, brand_name: str) -> List[Dict[str, Any]]:
        """Generate brand-based keywords"""
        keywords = []

        # Core brand keywords
        brand_keywords = [
            f"{brand_name}",
            f"{brand_name} products",
            f"{brand_name} store",
            f"{brand_name} online",
            f"{brand_name} shop",
            f"{brand_name} buy",
            f"{brand_name} official",
            f"{brand_name} website",
            f"buy {brand_name}",
            f"shop {brand_name}",
            f"{brand_name} ecommerce"
        ]

        for keyword in brand_keywords:
            keywords.append({
                'keyword': keyword.lower(),
                'relevance_score': 1.0,
                'source': 'brand_generation',
                'search_volume': 'unknown',
                'competition': 'unknown'
            })

        return keywords

    async def _query_serper_api(self, query: str) -> Optional[Dict]:
        """Query Serper API for real search data"""
        if not self.session:
            return None

        try:
            headers = {
                'X-API-KEY': self.serper_api_key,
                'Content-Type': 'application/json'
            }

            payload = {
                'q': query,
                'gl': 'us',  # Country
                'hl': 'en',  # Language
                'num': 10    # Number of results
            }

            async with self.session.post(
                'https://google.serper.dev/search',
                headers=headers,
                json=payload,
                timeout=10
            ) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    logger.warning(f"⚠️ Serper API returned status {response.status}")
                    return None

        except Exception as e:
            logger.error(f"❌ Serper API error: {e}")
            return None

    def _extract_keywords_from_serper(self, serper_data: Dict, original_query: str) -> List[Dict[str, Any]]:
        """Extract keywords from Serper API response"""
        keywords = []

        try:
            # Extract from organic results
            organic_results = serper_data.get('organic', [])

            for result in organic_results:
                title = result.get('title', '')
                snippet = result.get('snippet', '')

                # Extract keywords from title and snippet
                text = f"{title} {snippet}".lower()

                # Find relevant keywords (4+ characters, not common words)
                words = re.findall(r'\b\w{4,}\b', text)

                for word in words:
                    if word not in ['this', 'that', 'with', 'from', 'they', 'have', 'been', 'will']:
                        keywords.append({
                            'keyword': word,
                            'relevance_score': self._calculate_keyword_relevance(word, original_query),
                            'source': 'serper_organic',
                            'search_volume': 'unknown',  # Would need additional API for volume
                            'competition': 'unknown'
                        })

            # Extract from related searches
            related_searches = serper_data.get('relatedSearches', [])
            for related in related_searches:
                query_text = related.get('query', '')
                if query_text:
                    keywords.append({
                        'keyword': query_text,
                        'relevance_score': 0.8,
                        'source': 'serper_related',
                        'search_volume': 'unknown',
                        'competition': 'unknown'
                    })

        except Exception as e:
            logger.error(f"❌ Error extracting keywords from Serper data: {e}")

        return keywords

    def _calculate_keyword_relevance(self, keyword: str, original_query: str) -> float:
        """Calculate keyword relevance score"""
        try:
            # Simple relevance calculation
            if keyword.lower() in original_query.lower():
                return 1.0

            # Check for partial matches
            query_words = original_query.lower().split()
            if any(word in keyword.lower() for word in query_words):
                return 0.7

            return 0.3

        except:
            return 0.1

    def _deduplicate_keywords(self, keywords: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate keywords and sort by relevance"""
        if not keywords:
            return []

        # Group by keyword
        keyword_map = {}
        for kw in keywords:
            key = kw['keyword'].lower()
            if key not in keyword_map or kw['relevance_score'] > keyword_map[key]['relevance_score']:
                keyword_map[key] = kw

        # Sort by relevance score
        unique_keywords = list(keyword_map.values())
        unique_keywords.sort(key=lambda x: x['relevance_score'], reverse=True)

        return unique_keywords

    async def _analyze_technical_seo(self, url: str) -> Dict[str, Any]:
        """Analyze technical SEO factors following Google's guidelines"""
        logger.info(f"🔧 Analyzing technical SEO for: {url}")

        technical_analysis = {
            'url': url,
            'https_enabled': url.startswith('https://'),
            'mobile_friendly': 'unknown',
            'page_speed': 'unknown',
            'meta_tags': {},
            'headings_structure': {},
            'images_optimized': False,
            'structured_data': False,
            'sitemap_present': False,
            'robots_txt_present': False,
            'canonical_tags': False,
            'internal_links': 0,
            'external_links': 0
        }

        if not self.session:
            return technical_analysis

        try:
            async with self.session.get(url, timeout=15) as response:
                if response.status != 200:
                    technical_analysis['status_code'] = response.status
                    return technical_analysis

                html_content = await response.text()
                soup = BeautifulSoup(html_content, 'html.parser')

                # Analyze meta tags
                technical_analysis['meta_tags'] = self._analyze_meta_tags(soup)

                # Analyze heading structure
                technical_analysis['headings_structure'] = self._analyze_headings(soup)

                # Check for structured data
                technical_analysis['structured_data'] = bool(
                    soup.find_all('script', type='application/ld+json') or
                    soup.find_all(attrs={'itemtype': True})
                )

                # Check for canonical tags
                technical_analysis['canonical_tags'] = bool(
                    soup.find('link', rel='canonical')
                )

                # Count links
                internal_links = soup.find_all('a', href=re.compile(r'^/|' + re.escape(urlparse(url).netloc)))
                external_links = soup.find_all('a', href=re.compile(r'^https?://'))

                technical_analysis['internal_links'] = len(internal_links)
                technical_analysis['external_links'] = len(external_links) - len(internal_links)

                # Check images
                images = soup.find_all('img')
                images_with_alt = [img for img in images if img.get('alt')]
                technical_analysis['images_optimized'] = len(images_with_alt) / max(len(images), 1) > 0.8

        except Exception as e:
            logger.error(f"❌ Error in technical SEO analysis: {e}")
            technical_analysis['error'] = str(e)

        return technical_analysis

    def _analyze_meta_tags(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Analyze meta tags for SEO"""
        meta_analysis = {}

        # Title tag
        title_tag = soup.find('title')
        meta_analysis['title'] = {
            'content': title_tag.get_text(strip=True) if title_tag else '',
            'length': len(title_tag.get_text(strip=True)) if title_tag else 0,
            'optimal': 30 <= len(title_tag.get_text(strip=True)) <= 60 if title_tag else False
        }

        # Meta description
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        desc_content = meta_desc.get('content', '') if meta_desc else ''
        meta_analysis['description'] = {
            'content': desc_content,
            'length': len(desc_content),
            'optimal': 120 <= len(desc_content) <= 160
        }

        # Meta keywords (deprecated but still checked)
        meta_keywords = soup.find('meta', attrs={'name': 'keywords'})
        meta_analysis['keywords'] = meta_keywords.get('content', '') if meta_keywords else ''

        # Open Graph tags
        og_tags = {}
        for og_tag in soup.find_all('meta', property=re.compile(r'^og:')):
            property_name = og_tag.get('property', '').replace('og:', '')
            og_tags[property_name] = og_tag.get('content', '')
        meta_analysis['open_graph'] = og_tags

        return meta_analysis

    def _analyze_headings(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Analyze heading structure for SEO"""
        headings = {}

        for i in range(1, 7):  # H1 to H6
            tag_name = f'h{i}'
            heading_tags = soup.find_all(tag_name)
            headings[tag_name] = {
                'count': len(heading_tags),
                'content': [tag.get_text(strip=True) for tag in heading_tags[:5]]  # First 5
            }

        # Check for proper hierarchy
        has_h1 = headings.get('h1', {}).get('count', 0) > 0
        multiple_h1 = headings.get('h1', {}).get('count', 0) > 1

        headings['seo_analysis'] = {
            'has_h1': has_h1,
            'multiple_h1': multiple_h1,
            'proper_hierarchy': has_h1 and not multiple_h1
        }

        return headings

    async def _extract_brand_name(self, url: str) -> str:
        """Extract brand name from website"""
        try:
            # Try to extract from domain
            domain = urlparse(url).netloc
            domain_parts = domain.replace('www.', '').split('.')

            if domain_parts:
                brand_candidate = domain_parts[0].replace('-', ' ').title()
                return brand_candidate

            return "Unknown Brand"

        except:
            return "Unknown Brand"

    async def _find_brand_websites_improved(self, brand_name: str) -> List[str]:
        """Find official brand websites using multiple strategies"""
        logger.info(f"🌐 Finding websites for brand: {brand_name}")

        websites = []

        try:
            # Strategy 1: Try common brand website patterns
            common_patterns = [
                f"https://www.{brand_name.lower().replace(' ', '')}.com",
                f"https://{brand_name.lower().replace(' ', '')}.com",
                f"https://www.{brand_name.lower().replace(' ', '')}.net",
                f"https://shop.{brand_name.lower().replace(' ', '')}.com",
                f"https://store.{brand_name.lower().replace(' ', '')}.com"
            ]

            for pattern in common_patterns:
                if await self._test_website_accessibility(pattern):
                    websites.append(pattern)
                    logger.info(f"✅ Found accessible website: {pattern}")

            # Strategy 2: Try Google Custom Search if configured
            if self.google_cse_id:
                google_results = await self._search_google_custom(f"{brand_name} official website")
                websites.extend(google_results)

            # Strategy 3: Known major ecommerce sites for popular brands
            major_sites = await self._search_major_ecommerce_sites(brand_name)
            websites.extend(major_sites)

            # Remove duplicates and validate
            unique_websites = []
            seen = set()

            for website in websites:
                if website not in seen and self._is_valid_ecommerce_url(website):
                    unique_websites.append(website)
                    seen.add(website)

            logger.info(f"✅ Found {len(unique_websites)} potential brand websites")
            return unique_websites

        except Exception as e:
            logger.error(f"❌ Error finding brand websites: {e}")
            return []

    async def _test_website_accessibility(self, url: str) -> bool:
        """Test if a website is accessible"""
        try:
            if not self.session:
                return False

            headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
            }

            async with self.session.head(url, headers=headers, timeout=10) as response:
                return response.status == 200

        except:
            return False

    async def _search_major_ecommerce_sites(self, brand_name: str) -> List[str]:
        """Search for brand products on major ecommerce platforms"""
        major_sites = []

        # Known patterns for major ecommerce sites
        ecommerce_patterns = [
            f"https://www.amazon.com/s?k={brand_name.replace(' ', '+')}",
            f"https://www.bestbuy.com/site/searchpage.jsp?st={brand_name.replace(' ', '+')}",
            f"https://www.target.com/s?searchTerm={brand_name.replace(' ', '+')}",
            f"https://www.walmart.com/search?q={brand_name.replace(' ', '+')}",
            f"https://www.ebay.com/sch/i.html?_nkw={brand_name.replace(' ', '+')}"
        ]

        for pattern in ecommerce_patterns:
            if await self._test_website_accessibility(pattern):
                major_sites.append(pattern)

        return major_sites

    async def _search_google_custom(self, query: str) -> List[str]:
        """Search using Google Custom Search API"""
        if not self.google_cse_id or not self.google_api_key:
            return []

        try:
            if not self.session:
                return []

            params = {
                'key': self.google_api_key,
                'cx': self.google_cse_id,
                'q': query,
                'num': 5
            }

            async with self.session.get(
                'https://www.googleapis.com/customsearch/v1',
                params=params,
                timeout=10
            ) as response:

                if response.status == 200:
                    data = await response.json()
                    items = data.get('items', [])

                    return [item.get('link', '') for item in items if item.get('link')]
                else:
                    logger.warning(f"Google Custom Search API error: {response.status}")
                    return []

        except Exception as e:
            logger.error(f"Google Custom Search error: {e}")
            return []

    def _is_valid_ecommerce_url(self, url: str) -> bool:
        """Check if URL is likely an ecommerce site"""
        try:
            domain = urlparse(url).netloc.lower()

            # Skip common non-ecommerce domains
            skip_domains = [
                'wikipedia.org', 'facebook.com', 'twitter.com', 'instagram.com',
                'youtube.com', 'linkedin.com', 'pinterest.com', 'reddit.com'
            ]

            for skip_domain in skip_domains:
                if skip_domain in domain:
                    return False

            return True

        except:
            return False

    async def _analyze_content_structure(self, url: str) -> Dict[str, Any]:
        """Analyze content structure for SEO"""
        content_analysis = {
            'word_count': 0,
            'paragraph_count': 0,
            'list_count': 0,
            'image_count': 0,
            'video_count': 0,
            'readability_score': 'unknown',
            'content_quality': 'unknown'
        }

        if not self.session:
            return content_analysis

        try:
            async with self.session.get(url, timeout=15) as response:
                if response.status != 200:
                    return content_analysis

                html_content = await response.text()
                soup = BeautifulSoup(html_content, 'html.parser')

                # Remove script and style elements
                for script in soup(["script", "style"]):
                    script.decompose()

                # Get text content
                text_content = soup.get_text()
                words = text_content.split()
                content_analysis['word_count'] = len(words)

                # Count structural elements
                content_analysis['paragraph_count'] = len(soup.find_all('p'))
                content_analysis['list_count'] = len(soup.find_all(['ul', 'ol']))
                content_analysis['image_count'] = len(soup.find_all('img'))
                content_analysis['video_count'] = len(soup.find_all(['video', 'iframe']))

                # Basic content quality assessment
                if content_analysis['word_count'] > 300:
                    content_analysis['content_quality'] = 'good'
                elif content_analysis['word_count'] > 100:
                    content_analysis['content_quality'] = 'fair'
                else:
                    content_analysis['content_quality'] = 'poor'

        except Exception as e:
            logger.error(f"❌ Error analyzing content structure: {e}")

        return content_analysis

    async def _get_performance_metrics(self, url: str) -> Dict[str, Any]:
        """Get basic performance metrics"""
        performance = {
            'response_time': 'unknown',
            'status_code': 'unknown',
            'content_size': 'unknown',
            'compression_enabled': 'unknown'
        }

        if not self.session:
            return performance

        try:
            import time
            start_time = time.time()

            async with self.session.get(url, timeout=15) as response:
                end_time = time.time()

                performance['response_time'] = round((end_time - start_time) * 1000, 2)  # ms
                performance['status_code'] = response.status

                content = await response.read()
                performance['content_size'] = len(content)

                # Check for compression
                content_encoding = response.headers.get('content-encoding', '')
                performance['compression_enabled'] = 'gzip' in content_encoding or 'br' in content_encoding

        except Exception as e:
            logger.error(f"❌ Error getting performance metrics: {e}")

        return performance

    async def _analyze_brand_presence(self, brand_name: str) -> Dict[str, Any]:
        """Analyze brand presence and competition"""
        return {
            'technical_seo': {'brand_analysis': True},
            'content_analysis': {'brand_mentions': 'unknown'},
            'performance_metrics': {'brand_visibility': 'unknown'},
            'recommendations': [
                f"Optimize brand presence for '{brand_name}'",
                "Improve brand visibility in search results",
                "Enhance brand-related content"
            ]
        }

    def _generate_seo_recommendations(self, technical_seo: Dict, content_analysis: Dict, performance_metrics: Dict) -> List[str]:
        """Generate actionable SEO recommendations based on analysis"""
        recommendations = []

        # Technical SEO recommendations
        if not technical_seo.get('https_enabled', True):
            recommendations.append("🔒 Enable HTTPS for better security and SEO ranking")

        if not technical_seo.get('structured_data', False):
            recommendations.append("📊 Add structured data markup (JSON-LD) for better search visibility")

        if not technical_seo.get('canonical_tags', False):
            recommendations.append("🔗 Add canonical tags to prevent duplicate content issues")

        # Meta tags recommendations
        meta_tags = technical_seo.get('meta_tags', {})
        title_info = meta_tags.get('title', {})
        if not title_info.get('optimal', False):
            recommendations.append("📝 Optimize title tag length (30-60 characters)")

        desc_info = meta_tags.get('description', {})
        if not desc_info.get('optimal', False):
            recommendations.append("📄 Optimize meta description length (120-160 characters)")

        # Content recommendations
        word_count = content_analysis.get('word_count', 0)
        if word_count < 300:
            recommendations.append("📚 Increase content length for better SEO (minimum 300 words)")

        # Performance recommendations
        response_time = performance_metrics.get('response_time', 0)
        if isinstance(response_time, (int, float)) and response_time > 3000:
            recommendations.append("⚡ Improve page load speed (currently > 3 seconds)")

        if not performance_metrics.get('compression_enabled', False):
            recommendations.append("🗜️ Enable GZIP compression to reduce page size")

        # Image optimization
        if not technical_seo.get('images_optimized', False):
            recommendations.append("🖼️ Add alt text to all images for better accessibility and SEO")

        # Default recommendations if none found
        if not recommendations:
            recommendations = [
                "✅ Technical SEO looks good overall",
                "🔍 Continue monitoring SEO performance",
                "📈 Focus on creating high-quality content"
            ]

        return recommendations
