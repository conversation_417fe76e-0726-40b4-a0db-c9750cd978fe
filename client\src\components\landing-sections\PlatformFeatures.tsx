"use client"

import { Feature72 } from "@/components/ui/feature72"
import { SubtleGradient } from "@/components/ui/subtle-gradients"

export function PlatformFeatures() {
  const emmaFeatures = [
    {
      id: "marketplace-agentes",
      title: "Marketplace de Agentes",
      description: "Contrata agentes IA especializados para cada área de tu marketing. Desde copywriting hasta análisis de datos.",
      image: "/Marketplace de agentes.png"
    },
    {
      id: "content-builder",
      title: "Editor",
      description: "Suite completa de herramientas visuales con IA para crear contenido profesional sin límites creativos.",
      image: "/Editor.jpg"
    },
    {
      id: "emma-ai",
      title: "Emma AI",
      description: "Asistente de marketing inteligente que optimiza automáticamente tus campañas y contenido.",
      image: "/Emma.png"
    },
    {
      id: "visual-studio",
      title: "Visual Studio",
      description: "Crea textos, imágenes, videos y contenido optimizado para todas tus plataformas de marketing.",
      image: "/Visual Studio.jpg"
    },
    {
      id: "herramientas-marketing",
      title: "Herramientas de Marketing",
      description: "Análisis SEO, optimización de títulos, investigación de palabras clave y herramientas de conversión.",
      image: "/Herramientas de Marketing.jpg"
    },
    {
      id: "ads-central",
      title: "Ads Central",
      description: "Centro de creación de anuncios con IA especializada en publicidad para máximo ROI en todas las plataformas.",
      image: "/Anuncio.png"
    }
  ]

  return (
    <div className="relative">
      <SubtleGradient variant="gradient" position="top-right" size="lg" />
      <Feature72
        heading="Tu agencia completa de marketing en un solo lugar"
        description="Reemplaza equipos enteros con Emma Studio. La revolución del marketing digital ya está aquí."
        linkUrl="/auth"
        linkText="Empezar Ahora"
        features={emmaFeatures}
      />
    </div>
  )
}
