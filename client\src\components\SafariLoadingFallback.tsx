/**
 * Safari-specific loading fallback component
 * Provides a visible loading state when <PERSON><PERSON> has rendering issues
 */

import { useEffect, useState } from "react";
import { motion } from "framer-motion";

interface SafariLoadingFallbackProps {
  title?: string;
  message?: string;
  timeout?: number;
  onTimeout?: () => void;
}

export function SafariLoadingFallback({ 
  title = "Cargando Emma Studio",
  message = "Optimizando para Safari...",
  timeout = 10000,
  onTimeout
}: SafariLoadingFallbackProps) {
  const [showFallback, setShowFallback] = useState(false);
  const [timeoutReached, setTimeoutReached] = useState(false);

  useEffect(() => {
    // Show fallback immediately for Safari
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
    if (isSafari) {
      setShowFallback(true);
    }

    // Set timeout for fallback
    const timer = setTimeout(() => {
      setTimeoutReached(true);
      if (onTimeout) {
        onTimeout();
      }
    }, timeout);

    return () => clearTimeout(timer);
  }, [timeout, onTimeout]);

  if (!showFallback) return null;

  return (
    <div 
      className="fixed inset-0 bg-white z-[9999] flex flex-col items-center justify-center"
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'white',
        zIndex: 9999,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center'
      }}
    >
      {/* Emma Logo */}
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="mb-8"
      >
        <div 
          className="w-20 h-20 rounded-full bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] flex items-center justify-center"
          style={{
            width: '80px',
            height: '80px',
            borderRadius: '50%',
            background: 'linear-gradient(45deg, #3018ef, #dd3a5a)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <span 
            className="text-white font-bold text-2xl"
            style={{ color: 'white', fontWeight: 'bold', fontSize: '24px' }}
          >
            E
          </span>
        </div>
      </motion.div>

      {/* Loading Animation */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2 }}
        className="mb-6"
      >
        <div 
          className="w-8 h-8 border-4 border-gray-200 border-t-[#3018ef] rounded-full animate-spin"
          style={{
            width: '32px',
            height: '32px',
            border: '4px solid #e5e7eb',
            borderTop: '4px solid #3018ef',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite'
          }}
        />
      </motion.div>

      {/* Title */}
      <motion.h1
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="text-2xl font-bold text-gray-900 mb-2 text-center"
        style={{ fontSize: '24px', fontWeight: 'bold', color: '#111827', marginBottom: '8px', textAlign: 'center' }}
      >
        {title}
      </motion.h1>

      {/* Message */}
      <motion.p
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="text-gray-600 text-center max-w-md"
        style={{ color: '#6b7280', textAlign: 'center', maxWidth: '384px' }}
      >
        {message}
      </motion.p>

      {/* Timeout Message */}
      {timeoutReached && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg max-w-md"
          style={{
            marginTop: '32px',
            padding: '16px',
            backgroundColor: '#fefce8',
            border: '1px solid #fde047',
            borderRadius: '8px',
            maxWidth: '384px'
          }}
        >
          <p 
            className="text-yellow-800 text-sm text-center"
            style={{ color: '#92400e', fontSize: '14px', textAlign: 'center' }}
          >
            La carga está tomando más tiempo de lo esperado. 
            Si el problema persiste, intenta recargar la página.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="mt-3 w-full bg-[#3018ef] text-white py-2 px-4 rounded-lg hover:bg-[#2516d6] transition-colors"
            style={{
              marginTop: '12px',
              width: '100%',
              backgroundColor: '#3018ef',
              color: 'white',
              padding: '8px 16px',
              borderRadius: '8px',
              border: 'none',
              cursor: 'pointer',
              transition: 'background-color 0.2s'
            }}
          >
            Recargar Página
          </button>
        </motion.div>
      )}

      {/* Safari-specific CSS */}
      <style>{`
        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
}

export default SafariLoadingFallback;
