import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Loader2, ShoppingCart, TrendingUp, Target, BarChart3, Search, Star, Package } from 'lucide-react';

interface KeywordResult {
  keyword: string;
  relevance_score: number;
  category: string;
}

interface ProductData {
  title: string;
  description: string;
  category: string;
  price: number;
  attributes: string;
}

interface AnalysisResult {
  brand_name: string;
  total_products: number;
  keywords: KeywordResult[];
  products: ProductData[];
  status: string;
  message: string;
}

const SEOEcommerceOptimizer: React.FC = () => {
  const [brandName, setBrandName] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<AnalysisResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const analyzeBrand = async () => {
    if (!brandName.trim()) {
      setError('Por favor ingresa un nombre de marca');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      // Use the new Real SEO Ecommerce API for authentic analysis
      const response = await fetch('/api/real-seo-ecommerce/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          brand_name: brandName,
          analysis_type: 'comprehensive',
          include_competitors: false
        }),
      });

      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      // Convert the real API response to the expected format
      const convertedResult: AnalysisResult = {
        brand_name: data.brand_name,
        total_products: data.total_products,
        keywords: data.seo_keywords.map((kw: any) => ({
          keyword: kw.keyword,
          relevance_score: kw.relevance_score,
          category: kw.source
        })),
        products: data.real_products.map((product: any) => ({
          title: product.title,
          description: product.description,
          category: product.category,
          price: product.price || 0,
          attributes: `Brand: ${product.brand}, SKU: ${product.sku || 'N/A'}, Availability: ${product.availability}`
        })),
        status: data.status,
        message: data.message
      };

      setResult(convertedResult);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      analyzeBrand();
    }
  };

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center space-x-3">
          <div className="p-3 bg-gradient-to-r from-emerald-500 to-blue-600 rounded-xl">
            <ShoppingCart className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-emerald-600 to-blue-600 bg-clip-text text-transparent">
            SEO Ecommerce Optimizer
          </h1>
        </div>
        <p className="text-gray-600 text-lg max-w-3xl mx-auto">
          Optimiza tu tienda online con IA avanzada. Analiza productos, genera keywords SEO y mejora títulos para aumentar ventas.
        </p>
        
        {/* Features badges */}
        <div className="flex flex-wrap justify-center gap-2 mt-4">
          <Badge variant="secondary" className="bg-emerald-100 text-emerald-800 border-emerald-200">
            <Package className="w-3 h-3 mr-1" />
            Análisis de Productos
          </Badge>
          <Badge variant="secondary" className="bg-blue-100 text-blue-800 border-blue-200">
            <Search className="w-3 h-3 mr-1" />
            Keywords SEO
          </Badge>
          <Badge variant="secondary" className="bg-purple-100 text-purple-800 border-purple-200">
            <TrendingUp className="w-3 h-3 mr-1" />
            Optimización IA
          </Badge>
          <Badge variant="secondary" className="bg-orange-100 text-orange-800 border-orange-200">
            <Star className="w-3 h-3 mr-1" />
            Competencia
          </Badge>
        </div>
      </div>

      {/* Input Section */}
      <Card className="border-2 border-dashed border-emerald-200 hover:border-emerald-300 transition-colors bg-gradient-to-r from-emerald-50 to-blue-50">
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Input
                type="text"
                placeholder="Ingresa el nombre de tu marca o tienda (ej: Nike, Adidas, MiTienda)"
                value={brandName}
                onChange={(e) => setBrandName(e.target.value)}
                onKeyPress={handleKeyPress}
                className="text-lg h-12 border-emerald-200 focus:border-emerald-400"
                disabled={loading}
              />
            </div>
            <Button
              onClick={analyzeBrand}
              disabled={loading || !brandName.trim()}
              className="h-12 px-8 bg-gradient-to-r from-emerald-500 to-blue-600 hover:from-emerald-600 hover:to-blue-700"
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                  Analizando Productos...
                </>
              ) : (
                <>
                  <ShoppingCart className="mr-2 h-5 w-5" />
                  Optimizar Ecommerce
                </>
              )}
            </Button>
          </div>
          
          {/* Real data notice */}
          <div className="mt-4 p-4 bg-emerald-100 rounded-lg border border-emerald-200">
            <div className="flex items-start space-x-3">
              <div className="p-1 bg-emerald-200 rounded-full">
                <Package className="h-4 w-4 text-emerald-700" />
              </div>
              <div>
                <p className="text-sm text-emerald-800 font-medium">
                  ✅ <strong>Análisis con Datos Reales:</strong> Sin simulaciones
                </p>
                <p className="text-xs text-emerald-700 mt-1">
                  Usa APIs auténticas (Serper, Google Search) y sigue la metodología oficial de Google SEO
                </p>
                <p className="text-xs text-emerald-600 mt-1">
                  <strong>Prueba con:</strong> Nike, Apple, Amazon, o cualquier URL de ecommerce real
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <p className="text-red-700">❌ {error}</p>
          </CardContent>
        </Card>
      )}

      {/* Results */}
      {result && (
        <div className="space-y-6">
          {/* Summary Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="bg-gradient-to-r from-emerald-50 to-emerald-100 border-emerald-200">
              <CardContent className="p-6 text-center">
                <div className="flex items-center justify-center space-x-2 mb-3">
                  <Target className="h-6 w-6 text-emerald-600" />
                  <span className="font-semibold text-emerald-800">Marca Analizada</span>
                </div>
                <p className="text-2xl font-bold text-emerald-900">{result.brand_name}</p>
                <p className="text-sm text-emerald-700 mt-1">Tienda optimizada</p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
              <CardContent className="p-6 text-center">
                <div className="flex items-center justify-center space-x-2 mb-3">
                  <Package className="h-6 w-6 text-blue-600" />
                  <span className="font-semibold text-blue-800">Productos</span>
                </div>
                <p className="text-2xl font-bold text-blue-900">{result.total_products}</p>
                <p className="text-sm text-blue-700 mt-1">Catálogo analizado</p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-r from-purple-50 to-purple-100 border-purple-200">
              <CardContent className="p-6 text-center">
                <div className="flex items-center justify-center space-x-2 mb-3">
                  <TrendingUp className="h-6 w-6 text-purple-600" />
                  <span className="font-semibold text-purple-800">Keywords SEO</span>
                </div>
                <p className="text-2xl font-bold text-purple-900">{result.keywords.length}</p>
                <p className="text-sm text-purple-700 mt-1">Optimizaciones generadas</p>
              </CardContent>
            </Card>
          </div>

          {/* Keywords Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5 text-emerald-600" />
                <span>Keywords SEO para Ecommerce</span>
                <Badge variant="secondary" className="ml-2 bg-emerald-100 text-emerald-800">
                  {result.keywords.length} generados
                </Badge>
              </CardTitle>
              <p className="text-sm text-gray-600">
                Keywords optimizados para mejorar el posicionamiento de tus productos en buscadores
              </p>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {result.keywords.map((keyword, index) => (
                  <Badge
                    key={index}
                    variant="secondary"
                    className="px-3 py-2 text-sm bg-gradient-to-r from-emerald-100 to-blue-100 text-emerald-800 border border-emerald-200 hover:from-emerald-200 hover:to-blue-200 transition-colors cursor-pointer"
                  >
                    <Search className="w-3 h-3 mr-1" />
                    {keyword.keyword}
                  </Badge>
                ))}
              </div>
              
              {result.keywords.length > 0 && (
                <div className="mt-4 p-3 bg-emerald-50 rounded-lg border border-emerald-200">
                  <p className="text-sm text-emerald-700">
                    💡 <strong>Tip:</strong> Usa estos keywords en títulos de productos, descripciones y meta tags para mejorar tu SEO
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Products Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Package className="h-5 w-5 text-blue-600" />
                <span>Productos Analizados</span>
                <Badge variant="secondary" className="ml-2 bg-blue-100 text-blue-800">
                  {result.products.length} productos
                </Badge>
              </CardTitle>
              <p className="text-sm text-gray-600">
                Análisis detallado de tu catálogo con sugerencias de optimización
              </p>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                {result.products.map((product, index) => (
                  <div
                    key={index}
                    className="p-4 border border-gray-200 rounded-lg hover:border-emerald-300 transition-colors bg-gradient-to-r from-white to-emerald-50"
                  >
                    <div className="flex justify-between items-start mb-3">
                      <h3 className="font-semibold text-lg text-gray-900 flex items-center">
                        <Package className="w-4 h-4 mr-2 text-emerald-600" />
                        {product.title}
                      </h3>
                      <span className="text-xl font-bold text-emerald-600">${product.price}</span>
                    </div>
                    <p className="text-gray-600 mb-3">{product.description}</p>
                    <div className="flex flex-wrap gap-2">
                      <Badge variant="outline" className="text-xs border-blue-200 text-blue-700">
                        📂 {product.category}
                      </Badge>
                      {product.attributes.split(', ').map((attr, attrIndex) => (
                        <Badge key={attrIndex} variant="secondary" className="text-xs bg-emerald-100 text-emerald-700">
                          ✨ {attr}
                        </Badge>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Success Message */}
          <Card className="border-emerald-200 bg-emerald-50">
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <div className="p-1 bg-emerald-200 rounded-full">
                  <TrendingUp className="h-4 w-4 text-emerald-700" />
                </div>
                <p className="text-emerald-700 font-medium">✅ {result.message}</p>
              </div>
              <p className="text-sm text-emerald-600 mt-2 ml-7">
                Tu tienda está lista para optimizar. Implementa los keywords sugeridos para mejorar tu posicionamiento.
              </p>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default SEOEcommerceOptimizer;
