import React, { useRef } from "react";
import { Upload, <PERSON>I<PERSON>, Loader2 } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface ProductUploaderProps {
  productImage: string | null;
  isProcessingProduct: boolean;
  onProductUpload: (file: File) => void;
}

export function ProductUploader({ 
  productImage, 
  isProcessingProduct, 
  onProductUpload 
}: ProductUploaderProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      onProductUpload(file);
    }
  };

  return (
    <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-slate-800">
          <Upload className="w-5 h-5 text-[#3018ef]" />
          Subir Producto
        </CardTitle>
        <CardDescription>
          Sube la imagen de tu producto (PNG, JPEG, WEBP)
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div
          className="border-2 border-dashed border-slate-300 rounded-xl p-8 text-center cursor-pointer hover:border-[#3018ef] hover:bg-[#3018ef]/5 transition-all"
          onClick={() => fileInputRef.current?.click()}
        >
          {isProcessingProduct ? (
            <div className="flex flex-col items-center gap-3">
              <Loader2 className="w-8 h-8 text-[#3018ef] animate-spin" />
              <p className="text-slate-600">Procesando imagen...</p>
            </div>
          ) : productImage ? (
            <div className="flex flex-col items-center gap-3">
              <img
                src={productImage}
                alt="Producto"
                className="max-w-24 max-h-24 object-contain rounded-lg"
              />
              <p className="text-sm text-slate-600">Producto cargado</p>
              <Button size="sm" variant="outline" onClick={() => fileInputRef.current?.click()}>
                Cambiar imagen
              </Button>
            </div>
          ) : (
            <div className="flex flex-col items-center gap-3">
              <ImageIcon className="w-12 h-12 text-slate-400" />
              <p className="text-slate-600">Haz clic para subir imagen</p>
              <p className="text-xs text-slate-500">PNG, JPEG o WEBP</p>
            </div>
          )}
        </div>
        <input
          ref={fileInputRef}
          type="file"
          accept="image/png,image/jpeg,image/jpg,image/webp"
          onChange={handleFileSelect}
          className="hidden"
        />
      </CardContent>
    </Card>
  );
}
