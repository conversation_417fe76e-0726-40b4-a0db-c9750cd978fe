"""
Google Search API integration for REAL product discovery
Uses Google Custom Search API to find actual products from websites
"""

import requests
import re
from urllib.parse import urlparse, urljoin
from bs4 import BeautifulSoup
import time

class GoogleSearchScraper:
    def __init__(self, api_key: str = None, search_engine_id: str = None):
        # Emma Studio Google API key (real key from memories)
        self.api_key = api_key or "AIzaSyBAF5GT1Isn2TBL-s9tUsdKQDI57Y8uQ18"
        # TODO: Create real Custom Search Engine ID for Emma Studio
        # This needs to be configured in Google Custom Search Console
        self.search_engine_id = search_engine_id or None  # Must be configured
        self.base_url = "https://www.googleapis.com/customsearch/v1"

        # Validate configuration
        if not self.search_engine_id:
            print("⚠️  WARNING: Google Custom Search Engine ID not configured")
            print("   Please create a CSE at: https://cse.google.com/cse/")
            print("   And set the search_engine_id parameter")
        
    def search_products_on_site(self, site_url: str, max_results: int = 10) -> list:
        """
        Search for products on a specific website using Google Custom Search
        
        Args:
            site_url: The website URL to search
            max_results: Maximum number of products to find
            
        Returns:
            List of real product dictionaries
        """
        try:
            print(f"🔍 Searching for products on {site_url} using Google API...")
            
            # Extract domain from URL
            domain = urlparse(site_url).netloc
            brand_name = self._extract_brand_from_domain(domain)
            
            # Search queries to find products
            search_queries = [
                f"site:{domain} producto",
                f"site:{domain} comprar",
                f"site:{domain} precio",
                f"site:{domain} tienda",
                f"site:{domain} shop",
                f"site:{domain} product"
            ]
            
            all_products = []
            
            for query in search_queries:
                try:
                    products = self._google_search(query, max_results=5)
                    if products:
                        all_products.extend(products)
                        print(f"✅ Found {len(products)} results for: {query}")
                    
                    time.sleep(1)  # Respect API limits
                    
                except Exception as e:
                    print(f"❌ Search error for '{query}': {e}")
                    continue
            
            # Process and extract product information
            real_products = []
            for item in all_products[:max_results]:
                try:
                    product = self._extract_product_from_search_result(item, brand_name)
                    if product:
                        real_products.append(product)
                except Exception as e:
                    print(f"❌ Error processing search result: {e}")
                    continue
            
            # Remove duplicates
            unique_products = self._remove_duplicates(real_products)
            
            print(f"🎯 Found {len(unique_products)} unique products")
            return unique_products
            
        except Exception as e:
            print(f"❌ Google search error: {e}")
            return []
    
    def _google_search(self, query: str, max_results: int = 10) -> list:
        """Perform Google Custom Search API call"""
        try:
            params = {
                'key': self.api_key,
                'cx': self.search_engine_id,
                'q': query,
                'num': min(max_results, 10),  # API limit
                'safe': 'active'
            }
            
            response = requests.get(self.base_url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            return data.get('items', [])
            
        except requests.exceptions.RequestException as e:
            print(f"❌ API request error: {e}")
            return []
        except Exception as e:
            print(f"❌ Search API error: {e}")
            return []
    
    def _extract_product_from_search_result(self, search_item: dict, brand_name: str) -> dict:
        """Extract product information from Google search result"""
        try:
            title = search_item.get('title', '')
            snippet = search_item.get('snippet', '')
            link = search_item.get('link', '')
            
            # Clean title (remove site name, etc.)
            clean_title = self._clean_product_title(title, brand_name)
            
            # Extract price from snippet if available
            price = self._extract_price_from_text(snippet)
            
            # Determine category
            category = self._determine_category_from_text(title + " " + snippet)
            
            # Create description
            description = snippet[:200] if snippet else f"Producto disponible en {brand_name}"
            
            # Extract attributes from snippet
            attributes = self._extract_attributes_from_text(snippet)
            
            return {
                "title": clean_title[:100],
                "description": description,
                "category": category,
                "price": price,
                "attributes": attributes,
                "url": link
            }
            
        except Exception as e:
            print(f"❌ Error extracting product: {e}")
            return None
    
    def _clean_product_title(self, title: str, brand_name: str) -> str:
        """Clean and format product title"""
        # Remove common suffixes
        title = re.sub(r'\s*-\s*.*$', '', title)  # Remove everything after dash
        title = re.sub(r'\s*\|\s*.*$', '', title)  # Remove everything after pipe
        title = re.sub(brand_name, '', title, flags=re.IGNORECASE)
        
        return title.strip()
    
    def _extract_price_from_text(self, text: str) -> float:
        """Extract price from text"""
        # Look for Mexican peso patterns
        peso_patterns = [
            r'\$\s*[\d,]+\.?\d*',  # $1,234.56
            r'[\d,]+\.?\d*\s*pesos?',  # 1234 pesos
            r'[\d,]+\.?\d*\s*MXN',  # 1234 MXN
        ]
        
        for pattern in peso_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                price_str = re.sub(r'[^\d.]', '', match.group())
                try:
                    return float(price_str)
                except:
                    continue
        
        # Default price range based on category
        if any(word in text.lower() for word in ['premium', 'pro', 'deluxe']):
            return 299.99
        elif any(word in text.lower() for word in ['basic', 'standard']):
            return 99.99
        else:
            return 199.99
    
    def _determine_category_from_text(self, text: str) -> str:
        """Determine product category from text"""
        text_lower = text.lower()
        
        categories = {
            "Pet Food": ["alimento", "comida", "food", "nutricion", "perro", "gato", "mascota"],
            "Pet Toys": ["juguete", "toy", "pelota", "hueso", "entretenimiento"],
            "Pet Accessories": ["collar", "correa", "cama", "casa", "transportadora", "accesorio"],
            "Electronics": ["telefono", "laptop", "tablet", "camara", "auriculares"],
            "Clothing": ["ropa", "camisa", "pantalon", "zapatos", "vestido"],
            "Home & Garden": ["hogar", "jardin", "muebles", "decoracion", "cocina"],
            "Sports": ["deporte", "fitness", "ejercicio", "gym", "atletico"],
            "Beauty": ["belleza", "cosmetico", "maquillaje", "skincare", "perfume"],
            "Books": ["libro", "manual", "guia", "literatura"],
            "Food": ["comida", "bebida", "cafe", "te", "snack"]
        }
        
        for category, keywords in categories.items():
            if any(keyword in text_lower for keyword in keywords):
                return category
        
        return "General"
    
    def _extract_attributes_from_text(self, text: str) -> str:
        """Extract product attributes from text"""
        # Look for common attribute patterns
        attributes = []
        
        # Size patterns
        size_match = re.search(r'(talla|size|tamaño):\s*([A-Z0-9-]+)', text, re.IGNORECASE)
        if size_match:
            attributes.append(f"Talla: {size_match.group(2)}")
        
        # Color patterns
        color_match = re.search(r'(color|colour):\s*(\w+)', text, re.IGNORECASE)
        if color_match:
            attributes.append(f"Color: {color_match.group(2)}")
        
        # Weight patterns
        weight_match = re.search(r'(\d+)\s*(kg|g|lb)', text, re.IGNORECASE)
        if weight_match:
            attributes.append(f"Peso: {weight_match.group(1)}{weight_match.group(2)}")
        
        return ", ".join(attributes) if attributes else "Disponible en línea"
    
    def _extract_brand_from_domain(self, domain: str) -> str:
        """Extract brand name from domain"""
        brand = domain.replace('www.', '').split('.')[0]
        return brand.replace('-', ' ').title()
    
    def _remove_duplicates(self, products: list) -> list:
        """Remove duplicate products based on title"""
        seen_titles = set()
        unique_products = []
        
        for product in products:
            title = product.get('title', '').lower().strip()
            if title and title not in seen_titles and len(title) > 3:
                seen_titles.add(title)
                unique_products.append(product)
        
        return unique_products


def search_real_products(site_url: str) -> list:
    """
    Main function to search for real products using Google API
    
    Args:
        site_url: Website URL to search
        
    Returns:
        List of real product dictionaries
    """
    scraper = GoogleSearchScraper()
    return scraper.search_products_on_site(site_url)


if __name__ == "__main__":
    # Test the Google search scraper
    test_url = "https://www.cuidadoconelperro.com.mx/"
    products = search_real_products(test_url)
    
    print(f"\n🎯 Found {len(products)} REAL products:")
    for i, product in enumerate(products, 1):
        print(f"{i}. {product['title']} - ${product['price']}")
        print(f"   Category: {product['category']}")
        print(f"   URL: {product['url']}")
        print()
