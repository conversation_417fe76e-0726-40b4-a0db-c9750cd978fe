"""
Ideogram service specifically optimized for Product Placement background generation.
Separate from the general Ideogram service and Photography service.
"""

import os
import httpx
import logging
from typing import Dict, Any, Optional
from app.core.config import settings

logger = logging.getLogger(__name__)


class IdeogramBackgroundService:
    """
    Specialized Ideogram service for generating clean backgrounds optimized for product placement.
    Uses specific settings and prompts designed for marketing backgrounds.
    """
    
    def __init__(self):
        """Initialize the Product Placement Ideogram service."""
        self.api_key = os.getenv("IDEOGRAM_API_KEY") or settings.IDEOGRAM_API_KEY
        self.v3_endpoint = "https://api.ideogram.ai/v1/ideogram-v3/generate"
        
        if not self.api_key:
            logger.warning("Ideogram API key not configured for Product Placement backgrounds")
    
    async def generate_background_options(
        self,
        scene_prompt: str,
        style: str = "realistic",
        size: str = "1024x1024",
        num_options: int = 4,
        product_image_data: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Generate multiple background options specifically for product placement.
        
        Args:
            scene_prompt: Description of the background scene
            style: Style preference (realistic, minimalist, lifestyle, commercial)
            size: Image dimensions
            num_options: Number of background options to generate (default 4)
            
        Returns:
            Dict with success status, multiple background options, and metadata
        """
        if not self.api_key:
            logger.error("Ideogram API key not configured for Product Placement")
            return {"success": False, "error": "Ideogram API key not configured"}

        try:
            # Style-specific enhancements for product placement backgrounds
            style_enhancements = {
                "realistic": "Professional commercial photography, realistic lighting, clean composition, high-end marketing quality",
                "minimalist": "Clean minimal background, simple composition, neutral colors, professional studio lighting, uncluttered",
                "lifestyle": "Lifestyle photography, natural authentic environment, warm lighting, inviting atmosphere",
                "commercial": "Commercial photography, professional studio setup, perfect lighting, premium marketing quality"
            }
            
            enhancement = style_enhancements.get(style, style_enhancements["realistic"])
            
            # Optimized prompt for product placement backgrounds
            optimized_prompt = f"""A professional {scene_prompt} background scene perfect for product placement.
            
{enhancement}. Clean background environment without any products, text, logos, or branding visible. 
Professional marketing photography quality, suitable for placing products in the foreground. 
High resolution, sharp focus, excellent composition for commercial use.

IMPORTANT: This should be a clean background scene only - no products, text, words, letters, or branding should be visible."""

            # Convert size to aspect ratio
            aspect_ratio = self._size_to_aspect_ratio(size)

            # Product Placement specific configuration
            files = {
                "prompt": (None, optimized_prompt),
                "aspect_ratio": (None, aspect_ratio),
                "rendering_speed": (None, "QUALITY"),  # Highest quality for marketing
                "magic_prompt": (None, "ON"),  # Enhanced prompt processing
                "style_type": (None, "REALISTIC"),  # Realistic style for commercial use
                "negative_prompt": (None, "products, items, objects, text, words, letters, logos, branding, watermarks, people, faces, hands, cluttered, messy, low quality"),
                "num_images": (None, str(num_options))  # Generate multiple options
            }

            # Add product image as style reference if provided
            if product_image_data:
                try:
                    # Convert base64 to bytes for upload
                    if product_image_data.startswith('data:image'):
                        # Remove data URL prefix
                        image_data = product_image_data.split(',')[1]
                    else:
                        image_data = product_image_data

                    import base64
                    image_bytes = base64.b64decode(image_data)

                    # Add as style reference image
                    files["style_reference_images"] = ("product_reference.png", image_bytes, "image/png")

                    logger.info(f"🖼️ Using product image as style reference for Product Placement backgrounds")
                except Exception as e:
                    logger.warning(f"Could not process product image as reference: {e}")
                    # Continue without reference image

            headers = {
                "Api-Key": self.api_key
            }

            logger.info(f"🎨 Generating {num_options} Product Placement backgrounds: {scene_prompt}")
            logger.info(f"📐 Style: {style}, Aspect ratio: {aspect_ratio}")

            # Use shorter timeout specifically for Product Placement
            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(
                    self.v3_endpoint,
                    files=files,
                    headers=headers
                )

                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"Ideogram Product Placement error {response.status_code}: {error_text}")
                    return {"success": False, "error": f"Ideogram error: {error_text}"}

                result = response.json()

                # Process multiple background options for Product Placement
                if "data" in result and len(result["data"]) > 0:
                    background_options = []
                    logger.info(f"📊 Processing {len(result['data'])} Product Placement background options")
                    logger.info(f"🔍 Raw result data: {[img.get('url', 'NO_URL') for img in result['data']]}")

                    # Process only the first few to avoid timeout
                    max_options = min(num_options, len(result["data"]), 4)
                    logger.info(f"🎯 Will process {max_options} options")

                    for i, image_data in enumerate(result["data"][:max_options]):
                        try:
                            # Download and convert each background option
                            image_url = image_data.get("url")
                            logger.info(f"🔄 Processing image {i+1}: {image_url}")

                            if image_url:
                                # Use shorter timeout for Product Placement
                                logger.info(f"📥 Downloading image {i+1}...")
                                image_response = await client.get(image_url, timeout=30.0)
                                logger.info(f"📊 Download response {i+1}: {image_response.status_code}")

                                if image_response.status_code == 200:
                                    import base64
                                    image_base64 = base64.b64encode(image_response.content).decode('utf-8')
                                    image_data_url = f"data:image/png;base64,{image_base64}"

                                    background_options.append({
                                        "url": image_url,
                                        "data": image_data_url,
                                        "seed": image_data.get("seed", 0),
                                        "prompt": image_data.get("prompt", optimized_prompt),
                                        "is_image_safe": image_data.get("is_image_safe", True),
                                        "style_type": "REALISTIC"
                                    })

                                    logger.info(f"✅ Processed Product Placement background {i+1}/{max_options}")
                                else:
                                    logger.warning(f"Failed to download Product Placement background {i+1}")
                        except Exception as e:
                            logger.error(f"Error processing Product Placement background {i+1}: {e}")
                            continue

                    if background_options:
                        logger.info(f"✅ Successfully processed {len(background_options)} Product Placement backgrounds")
                        logger.info(f"🎯 Returning {len(background_options)} options in 'images' array")

                        return {
                            "success": True,
                            "image_data": background_options[0]["data"],  # Primary background
                            "image_url": background_options[0]["url"],
                            "images": background_options,  # All options for selection
                            "revised_prompt": background_options[0]["prompt"],
                            "metadata": {
                                "model": "ideogram-3.0-product-placement",
                                "type": "product_placement_background",
                                "scene_type": scene_prompt,
                                "style": style,
                                "size": size,
                                "aspect_ratio": aspect_ratio,
                                "num_requested": num_options,
                                "num_generated": len(background_options),
                                "total_images": len(background_options),
                                "optimized_for": "product_placement",
                                "rendering_speed": "QUALITY",
                                "magic_prompt": "ON",
                                "style_type": "REALISTIC",
                                "original_prompt": scene_prompt,
                                "enhanced_prompt": optimized_prompt
                            }
                        }
                    else:
                        return {"success": False, "error": "No background options could be processed"}
                else:
                    return {"success": False, "error": "No background data received from Ideogram"}

        except Exception as e:
            logger.error(f"Error generating product placement backgrounds: {e}", exc_info=True)
            return {"success": False, "error": f"Background generation failed: {str(e)}"}
    
    def _size_to_aspect_ratio(self, size: str) -> str:
        """Convert size string to Ideogram aspect ratio format."""
        if "x" in size:
            width_str, height_str = size.split("x")
            width = int(width_str)
            height = int(height_str)
            
            if width == height:
                return "1x1"
            elif width > height:
                if width / height >= 1.7:
                    return "16x9"
                else:
                    return "3x2"
            else:
                if height / width >= 1.7:
                    return "9x16"
                else:
                    return "2x3"
        else:
            return "1x1"


# Global instance
ideogram_background_service = IdeogramBackgroundService()
