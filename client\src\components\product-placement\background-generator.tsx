import React, { useState } from "react";
import { <PERSON>, Sparkles, Loader2 } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { SceneSuggestion, Style } from "@/services/product-placement-service";

interface BackgroundGeneratorProps {
  sceneSuggestions: SceneSuggestion[];
  styles: Style[];
  selectedStyle: string;
  isGeneratingBackground: boolean;
  onStyleChange: (style: string) => void;
  onGenerateBackground: (prompt: string) => void;
}

export function BackgroundGenerator({
  sceneSuggestions,
  styles,
  selectedStyle,
  isGeneratingBackground,
  onStyleChange,
  onGenerateBackground,
}: BackgroundGeneratorProps) {
  const [customPrompt, setCustomPrompt] = useState("");

  return (
    <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-slate-800">
          <Camera className="w-5 h-5 text-[#dd3a5a]" />
          Generar Fondo
        </CardTitle>
        <CardDescription>
          Elige una escena predefinida o describe tu propia escena
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Style Selection */}
        <div>
          <Label className="text-sm font-medium text-slate-700">Estilo</Label>
          <Select value={selectedStyle} onValueChange={onStyleChange}>
            <SelectTrigger className="mt-1">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {styles.map((style) => (
                <SelectItem key={style.id} value={style.id}>
                  <div>
                    <div className="font-medium">{style.name}</div>
                    <div className="text-xs text-slate-500">{style.description}</div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Scene Suggestions */}
        <div>
          <Label className="text-sm font-medium text-slate-700">Escenas Sugeridas</Label>
          <div className="grid grid-cols-2 gap-2 mt-2">
            {sceneSuggestions.slice(0, 6).map((scene) => (
              <Button
                key={scene.id}
                variant="outline"
                size="sm"
                className="h-auto p-3 text-left justify-start hover:bg-[#3018ef]/5 hover:border-[#3018ef] hover:scale-105 transition-all duration-200 backdrop-blur-sm"
                onClick={() => onGenerateBackground(scene.prompt)}
                disabled={isGeneratingBackground}
              >
                <div>
                  <div className="font-medium text-xs">{scene.name}</div>
                  <div className="text-xs text-slate-500 capitalize">{scene.category}</div>
                </div>
              </Button>
            ))}
          </div>
        </div>

        {/* Custom Prompt */}
        <div>
          <Label className="text-sm font-medium text-slate-700">Escena Personalizada</Label>
          <Textarea
            placeholder="Describe tu escena ideal (ej: moderna cocina con mármol blanco y luz natural)"
            value={customPrompt}
            onChange={(e) => setCustomPrompt(e.target.value)}
            className="mt-1 resize-none"
            rows={3}
          />
          <Button
            className="w-full mt-2 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] hover:opacity-90 hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl"
            onClick={() => onGenerateBackground(customPrompt)}
            disabled={!customPrompt.trim() || isGeneratingBackground}
          >
            {isGeneratingBackground ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Generando...
              </>
            ) : (
              <>
                <Sparkles className="w-4 h-4 mr-2" />
                Generar Fondo
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
