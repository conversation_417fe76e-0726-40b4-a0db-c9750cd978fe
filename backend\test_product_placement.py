#!/usr/bin/env python3
"""
Test script for Product Placement Service
Verifies that the service is properly configured and can generate backgrounds.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from app.services.product_placement_service import product_placement_service
from app.core.config import settings


async def test_product_placement_service():
    """Test the Product Placement service functionality."""
    
    print("🧪 Testing Product Placement Service")
    print("=" * 50)
    
    # Test 1: Check API key configuration
    print("1. Checking Ideogram API key configuration...")
    api_key = os.getenv("IDEOGRAM_API_KEY") or settings.IDEOGRAM_API_KEY
    if api_key:
        print(f"   ✅ API key configured: {api_key[:10]}...")
    else:
        print("   ❌ API key not configured")
        return False
    
    # Test 2: Get scene suggestions
    print("\n2. Testing scene suggestions...")
    try:
        suggestions = await product_placement_service.get_scene_suggestions()
        print(f"   ✅ Retrieved {len(suggestions)} scene suggestions")
        for suggestion in suggestions[:3]:
            print(f"      - {suggestion['name']}: {suggestion['prompt'][:50]}...")
    except Exception as e:
        print(f"   ❌ Error getting scene suggestions: {e}")
        return False
    
    # Test 3: Test background generation (simple test)
    print("\n3. Testing background generation...")
    try:
        result = await product_placement_service.generate_background_scene(
            scene_prompt="modern kitchen with marble countertops",
            style="realistic",
            size="1024x1024"
        )
        
        if result["success"]:
            print("   ✅ Background generation successful")
            if "image_data" in result:
                print(f"      - Image data length: {len(result['image_data'])} characters")
            if "metadata" in result:
                print(f"      - Metadata: {result['metadata']}")
        else:
            print(f"   ❌ Background generation failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error testing background generation: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 All tests passed! Product Placement service is working correctly.")
    return True


async def test_api_endpoints():
    """Test the API endpoints are properly registered."""
    
    print("\n🌐 Testing API Endpoint Registration")
    print("=" * 50)
    
    try:
        import httpx
        
        # Test health endpoint
        print("1. Testing health endpoint...")
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get("http://localhost:8000/api/product-placement/health")
                if response.status_code == 200:
                    data = response.json()
                    print(f"   ✅ Health endpoint working: {data.get('status', 'unknown')}")
                else:
                    print(f"   ⚠️  Health endpoint returned status {response.status_code}")
            except httpx.ConnectError:
                print("   ⚠️  Server not running - skipping endpoint tests")
                return True
            except Exception as e:
                print(f"   ❌ Error testing health endpoint: {e}")
                return False
        
        # Test scene suggestions endpoint
        print("2. Testing scene suggestions endpoint...")
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get("http://localhost:8000/api/product-placement/scene-suggestions")
                if response.status_code == 200:
                    data = response.json()
                    if data.get("success") and "suggestions" in data:
                        print(f"   ✅ Scene suggestions endpoint working: {len(data['suggestions'])} suggestions")
                    else:
                        print("   ❌ Scene suggestions endpoint returned invalid data")
                        return False
                else:
                    print(f"   ❌ Scene suggestions endpoint returned status {response.status_code}")
                    return False
            except Exception as e:
                print(f"   ❌ Error testing scene suggestions endpoint: {e}")
                return False
        
        print("\n" + "=" * 50)
        print("🎉 API endpoints are working correctly!")
        return True
        
    except ImportError:
        print("   ⚠️  httpx not available - skipping endpoint tests")
        return True


if __name__ == "__main__":
    async def main():
        success = True
        
        # Test service functionality
        success &= await test_product_placement_service()
        
        # Test API endpoints
        success &= await test_api_endpoints()
        
        if success:
            print("\n🎊 All Product Placement tests completed successfully!")
            print("\nNext steps:")
            print("1. Start the backend server: uvicorn app.main:app --reload")
            print("2. Start the frontend: npm run dev")
            print("3. Navigate to: /dashboard/herramientas/product-placement")
            print("4. Test the complete workflow!")
        else:
            print("\n❌ Some tests failed. Please check the configuration.")
            sys.exit(1)
    
    asyncio.run(main())
