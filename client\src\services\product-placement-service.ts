/**
 * Product Placement Service - Frontend API client
 * Handles communication with the backend product placement endpoints
 */

export interface SceneSuggestion {
  id: string;
  name: string;
  prompt: string;
  category: string;
}

export interface Style {
  id: string;
  name: string;
  description: string;
}

export interface BackgroundOption {
  url: string;
  data: string;
  seed: number;
  prompt: string;
  is_image_safe: boolean;
  style_type: string;
}

export interface ProductPlacementResponse {
  success: boolean;
  image_data?: string;
  image_url?: string;
  images?: BackgroundOption[];
  revised_prompt?: string;
  metadata?: {
    [key: string]: any;
  };
  error?: string;
}

export interface BackgroundGenerationRequest {
  scene_prompt: string;
  style: string;
  size: string;
  product_image_data?: string; // Optional product image for style reference
}

export interface ProductCompositeRequest {
  background_url: string;
  product_image_data: string;
  position: { x: number; y: number };
  scale: number;
  rotation: number;
  shadow_intensity: number;
}

class ProductPlacementService {
  private baseUrl = "/api/product-placement";

  /**
   * Generate a background scene using Ideogram AI
   */
  async generateBackground(request: BackgroundGenerationRequest): Promise<ProductPlacementResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/generate-background`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("❌ API Error response:", errorText);
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error("❌ Error generating background:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido",
      };
    }
  }

  /**
   * Upload and process a product image
   */
  async uploadProduct(file: File): Promise<ProductPlacementResponse> {
    try {
      const formData = new FormData();
      formData.append("product_image", file);

      const response = await fetch(`${this.baseUrl}/upload-product`, {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error uploading product:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido",
      };
    }
  }

  /**
   * Composite product on background with positioning
   */
  async compositeProduct(request: ProductCompositeRequest): Promise<ProductPlacementResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/composite`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error compositing product:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido",
      };
    }
  }

  /**
   * Get predefined scene suggestions
   */
  async getSceneSuggestions(): Promise<{ success: boolean; suggestions?: SceneSuggestion[]; error?: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/scene-suggestions`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error getting scene suggestions:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido",
      };
    }
  }

  /**
   * Get available styles for background generation
   */
  async getStyles(): Promise<{ success: boolean; styles?: Style[]; error?: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/styles`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error getting styles:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido",
      };
    }
  }

  /**
   * Check service health
   */
  async healthCheck(): Promise<{ success: boolean; status?: string; error?: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/health`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error checking service health:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido",
      };
    }
  }

  /**
   * Download image from data URL
   */
  downloadImage(imageData: string, filename: string = "product-placement.png"): void {
    try {
      const link = document.createElement("a");
      link.href = imageData;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error("Error downloading image:", error);
    }
  }

  /**
   * Convert file to base64 data URL
   */
  async fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  /**
   * Validate image file
   */
  validateImageFile(file: File): { valid: boolean; error?: string } {
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = ["image/png", "image/jpeg", "image/jpg", "image/webp"];

    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: "Formato de archivo no soportado. Use PNG, JPEG o WEBP.",
      };
    }

    if (file.size > maxSize) {
      return {
        valid: false,
        error: "El archivo es demasiado grande. Máximo 10MB.",
      };
    }

    return { valid: true };
  }
}

// Export singleton instance
export const productPlacementService = new ProductPlacementService();
