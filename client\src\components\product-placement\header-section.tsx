import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, Sparkles } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface HeaderSectionProps {
  productImage: string | null;
  backgroundImage: string | null;
  compositeImage: string | null;
  onBack: () => void;
}

export function HeaderSection({ 
  productImage, 
  backgroundImage, 
  compositeImage, 
  onBack 
}: HeaderSectionProps) {
  return (
    <div className="bg-white/80 backdrop-blur-sm border-b border-white/20 sticky top-0 z-10">
      <div className="max-w-7xl mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={onBack}
              className="text-slate-600 hover:text-[#3018ef]"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Volver a Ads Central
            </Button>
            <div className="h-6 w-px bg-slate-200" />
            <div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">
                Product Placement
              </h1>
              <p className="text-slate-600 text-sm">
                Coloca tus productos en escenas realistas para marketing
              </p>
              
              {/* Progress Steps */}
              <div className="flex items-center gap-2 mt-3">
                <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs ${
                  productImage ? 'bg-green-100 text-green-700' : 'bg-slate-100 text-slate-500'
                }`}>
                  <span className="font-bold">{productImage ? '✓' : '1'}</span>
                  Subir Producto
                </div>
                <span className="text-slate-300">→</span>
                <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs ${
                  backgroundImage ? 'bg-green-100 text-green-700' : 'bg-slate-100 text-slate-500'
                }`}>
                  <span className="font-bold">{backgroundImage ? '✓' : '2'}</span>
                  Generar Fondo
                </div>
                <span className="text-slate-300">→</span>
                <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs ${
                  compositeImage ? 'bg-green-100 text-green-700' : 'bg-slate-100 text-slate-500'
                }`}>
                  <span className="font-bold">{compositeImage ? '✓' : '3'}</span>
                  Composición Final
                </div>
              </div>
            </div>
          </div>
          <Badge variant="secondary" className="bg-[#3018ef]/10 text-[#3018ef] border-[#3018ef]/20">
            <Sparkles className="w-3 h-3 mr-1" />
            Emma Studio
          </Badge>
        </div>
      </div>
    </div>
  );
}
