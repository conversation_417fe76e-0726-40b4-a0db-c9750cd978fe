# 🔧 Guía Técnica - Nueva Landing Page Emma Studio

## 🚀 Implementación y Desarrollo

### Configuración del Proyecto

La nueva landing page está construida con las siguientes tecnologías:

```json
{
  "framework": "React 18 + TypeScript",
  "routing": "Wouter",
  "styling": "Tailwind CSS",
  "animations": "Framer Motion",
  "build": "Vite",
  "deployment": "Vercel/Netlify ready"
}
```

### Estructura de Componentes

#### 1. Componente Principal
```typescript
// client/src/pages/new-landing-page.tsx
export default function NewLandingPage() {
  return (
    <div className="min-h-screen bg-white overflow-x-hidden">
      <EmmaNavBar items={emmaNavItems} />
      <main>
        {/* Secciones con divisores animados */}
        <HeroSection />
        <SectionDivider variant="wave" color="gradient" />
        <PlatformFeatures />
        {/* ... más secciones */}
      </main>
      <FooterSection />
    </div>
  )
}
```

#### 2. Sistema de Divisores
```typescript
// Tipos de divisores disponibles
type DividerVariant = "wave" | "gradient" | "dots" | "zigzag"
type DividerColor = "blue" | "red" | "gradient"

// Uso
<SectionDivider variant="wave" color="gradient" />
```

#### 3. Animaciones con Framer Motion
```typescript
// Ejemplo de animación de entrada
<motion.div
  initial={{ opacity: 0, y: 20 }}
  whileInView={{ opacity: 1, y: 0 }}
  viewport={{ once: true }}
  transition={{ duration: 0.5 }}
>
  {/* Contenido */}
</motion.div>
```

## 🎨 Sistema de Diseño Técnico

### Colores CSS Custom Properties
```css
:root {
  --emma-blue: #3018ef;
  --emma-red: #dd3a5a;
  --emma-gradient: linear-gradient(135deg, #3018ef, #dd3a5a);
}
```

### Clases Tailwind Personalizadas
```css
/* Glassmorphism Emma */
.glass-emma {
  @apply bg-white/20 backdrop-blur-md border border-white/30;
}

/* Gradiente de texto Emma */
.text-emma-gradient {
  @apply bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent;
}

/* Botones Emma */
.btn-emma-blue {
  @apply bg-[#3018ef] hover:bg-[#2614d4] text-white;
}

.btn-emma-red {
  @apply bg-[#dd3a5a] hover:bg-[#c73351] text-white;
}
```

### Responsive Breakpoints
```typescript
// Tailwind breakpoints utilizados
const breakpoints = {
  sm: '640px',   // Móvil grande
  md: '768px',   // Tablet
  lg: '1024px',  // Desktop
  xl: '1280px',  // Desktop grande
  '2xl': '1536px' // Desktop extra grande
}
```

## 🖼️ Gestión de Imágenes Flotantes

### Configuración de Parallax
```typescript
// client/src/components/ui/parallax-floating.tsx
interface FloatingElementProps {
  depth: number;        // Profundidad del efecto (0.1 - 5.0)
  className?: string;   // Clases CSS adicionales
  style?: CSSProperties; // Estilos inline
  children: ReactNode;  // Contenido del elemento
}

// Uso en HeroSection
<FloatingElement
  depth={0.5}
  className="z-[11]"
  style={{ top: '10%', left: '5%' }}
>
  <img src="/imagen.jpg" alt="Marketing" />
</FloatingElement>
```

### Imágenes Utilizadas
```typescript
// Lista de imágenes flotantes del hero
const emmaImages = [
  { url: "/Anuncio.png", alt: "Anuncio publicitario" },
  { url: "/Astronauta.png", alt: "Astronauta creativo" },
  { url: "/Gato lentes.webp", alt: "Gato con lentes" },
  { url: "/Hombre real.jpeg", alt: "Persona real" },
  { url: "/Labial.jpg", alt: "Producto de belleza" },
  { url: "/PHOTO-2025-06-30-19-02-40.jpg", alt: "Fotografía profesional" },
  { url: "/libro.jpg", alt: "Libro educativo" },
  { url: "/poster.jpg", alt: "Poster promocional" }
];
```

## 🔄 Texto Rotativo

### Implementación TextRotate
```typescript
// client/src/components/ui/text-rotate.tsx
interface TextRotateProps {
  texts: string[];           // Array de textos a rotar
  interval?: number;         // Intervalo en ms (default: 3000)
  className?: string;        // Clases CSS
  animationDuration?: number; // Duración animación (default: 500)
}

// Uso en HeroSection
<TextRotate
  texts={[
    "increíble",
    "profesional", 
    "efectivo",
    "rentable",
    "viral"
  ]}
  interval={3000}
  className="text-emma-gradient"
/>
```

## 🧭 Navegación Emma

### Configuración del Navbar
```typescript
// client/src/components/ui/emma-navbar.tsx
export const emmaNavItems = [
  { label: "Inicio", href: "#hero" },
  { label: "Características", href: "#features" },
  { label: "Agentes", href: "#agents" },
  { label: "Precios", href: "#pricing" },
  { label: "FAQ", href: "#faq" }
];

// Navegación responsive con menú hamburguesa
<EmmaNavBar 
  items={emmaNavItems}
  logo="Emma Studio"
  ctaText="Empezar Ahora"
  ctaHref="/login"
/>
```

## 📱 Optimizaciones Mobile

### Estrategia Mobile-First
```css
/* Estilos base para móvil */
.hero-title {
  @apply text-3xl leading-tight;
}

/* Tablet */
@media (min-width: 768px) {
  .hero-title {
    @apply text-5xl;
  }
}

/* Desktop */
@media (min-width: 1024px) {
  .hero-title {
    @apply text-7xl;
  }
}
```

### Touch Optimizations
```typescript
// Eventos táctiles optimizados
const handleTouchStart = (e: TouchEvent) => {
  // Prevenir zoom accidental
  if (e.touches.length > 1) {
    e.preventDefault();
  }
};

// Botones con área táctil mínima de 44px
.btn-touch {
  @apply min-h-[44px] min-w-[44px] p-3;
}
```

## ⚡ Optimizaciones de Performance

### Lazy Loading de Componentes
```typescript
// Carga diferida de secciones pesadas
const HeroSection = lazy(() => import('./HeroSection'));
const AgentShowcase = lazy(() => import('./AgentShowcase'));

// Suspense wrapper
<Suspense fallback={<LoadingSpinner />}>
  <HeroSection />
</Suspense>
```

### Optimización de Imágenes
```typescript
// Configuración de imágenes optimizadas
const imageOptimization = {
  formats: ['webp', 'avif', 'jpg'],
  sizes: [320, 640, 768, 1024, 1280],
  quality: 85,
  loading: 'lazy' as const
};

// Componente de imagen optimizada
<OptimizedImage
  src="/imagen.jpg"
  alt="Descripción"
  width={400}
  height={300}
  {...imageOptimization}
/>
```

### Bundle Splitting
```typescript
// vite.config.ts
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          'landing-sections': [
            './src/components/landing-sections/HeroSection',
            './src/components/landing-sections/PlatformFeatures'
          ],
          'animations': ['framer-motion'],
          'ui-components': ['./src/components/ui']
        }
      }
    }
  }
});
```

## 🔍 SEO y Accessibility

### Meta Tags Optimizados
```typescript
// Configuración SEO
const seoConfig = {
  title: "Emma Studio - La Primera Agencia de Marketing Virtual",
  description: "Revoluciona tu marketing con Emma Studio. Agentes IA especializados, resultados reales, disponible 24/7. Ahorra hasta 80% vs agencias tradicionales.",
  keywords: "marketing digital, inteligencia artificial, agencia virtual, automatización marketing",
  ogImage: "/emma-og-image.jpg",
  twitterCard: "summary_large_image"
};
```

### Accessibility Features
```typescript
// Navegación por teclado
const handleKeyDown = (e: KeyboardEvent) => {
  if (e.key === 'Enter' || e.key === ' ') {
    handleClick();
  }
};

// ARIA labels
<button
  aria-label="Empezar con Emma Studio"
  role="button"
  tabIndex={0}
  onKeyDown={handleKeyDown}
>
  Empezar Ahora
</button>
```

## 🧪 Testing y QA

### Tests de Componentes
```typescript
// Ejemplo de test con React Testing Library
import { render, screen } from '@testing-library/react';
import { HeroSection } from './HeroSection';

test('renders hero section with correct title', () => {
  render(<HeroSection />);
  expect(screen.getByText(/Haz tu marketing/i)).toBeInTheDocument();
});
```

### Tests de Integración
```typescript
// Test de flujo completo
test('user can navigate through landing page', async () => {
  render(<NewLandingPage />);
  
  // Verificar hero
  expect(screen.getByText(/Haz tu marketing/i)).toBeInTheDocument();
  
  // Verificar CTAs
  const startButton = screen.getByText(/Empezar Ahora/i);
  expect(startButton).toBeInTheDocument();
  
  // Simular click
  fireEvent.click(startButton);
  // Verificar navegación...
});
```

## 🚀 Deployment

### Build Configuration
```json
{
  "scripts": {
    "build": "vite build",
    "preview": "vite preview",
    "build:analyze": "vite-bundle-analyzer"
  }
}
```

### Environment Variables
```bash
# .env.production
VITE_API_URL=https://api.emmastudio.com
VITE_ANALYTICS_ID=GA_MEASUREMENT_ID
VITE_HOTJAR_ID=HOTJAR_SITE_ID
```

---

**Documentación Técnica Completa**  
**Versión**: 2.0  
**Última Actualización**: Enero 2025  
**Mantenedor**: Equipo Emma Studio
