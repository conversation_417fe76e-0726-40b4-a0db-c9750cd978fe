"use client"

import React, { useEffect, useRef } from "react"

interface FloatingProps {
  children: React.ReactNode
  className?: string
}

interface FloatingElementProps {
  children: React.ReactNode
  depth?: number
  className?: string
  style?: React.CSSProperties
}

export const Floating: React.FC<FloatingProps> = ({
  children,
  className = ""
}) => {
  return (
    <div className={`relative ${className}`}>
      {children}
    </div>
  )
}

export const FloatingElement: React.FC<FloatingElementProps> = ({
  children,
  depth = 1,
  className = "",
  style = {}
}) => {
  const elementRef = useRef<HTMLDivElement>(null)

  // Safari-compatible positioning with enhanced error handling
  useEffect(() => {
    if (!elementRef.current) return;

    const element = elementRef.current;

    // Check if Safari
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

    const applySafariPositioning = () => {
      try {
        // Force absolute positioning with webkit prefixes
        element.style.setProperty('position', 'absolute', 'important');
        element.style.setProperty('-webkit-position', 'absolute', 'important');

        // Apply positioning with fallbacks
        if (style.top) {
          element.style.setProperty('top', String(style.top), 'important');
        }

        if (style.left) {
          element.style.setProperty('left', String(style.left), 'important');
          element.style.setProperty('right', 'auto', 'important');
        }

        if (style.right) {
          element.style.setProperty('right', String(style.right), 'important');
          element.style.setProperty('left', 'auto', 'important');
        }

        // Force visibility and display
        element.style.setProperty('display', 'block', 'important');
        element.style.setProperty('visibility', 'visible', 'important');
        element.style.setProperty('opacity', '1', 'important');

        // Safari-specific transform optimizations
        element.style.setProperty('-webkit-transform', 'translateZ(0)', 'important');
        element.style.setProperty('transform', 'translateZ(0)', 'important');
        element.style.setProperty('-webkit-backface-visibility', 'hidden', 'important');
        element.style.setProperty('backface-visibility', 'hidden', 'important');

        if (isSafari) {
          console.log('Safari floating element positioned:', {
            top: element.style.top,
            left: element.style.left,
            right: element.style.right,
            position: element.style.position
          });
        }
      } catch (error) {
        console.error('Error applying Safari positioning:', error);
      }
    };

    // Apply immediately
    applySafariPositioning();

    // Apply again after DOM updates for Safari
    if (isSafari) {
      const timeouts = [50, 100, 200, 500];
      timeouts.forEach(delay => {
        setTimeout(applySafariPositioning, delay);
      });
    }

    return () => {
      // Cleanup timeouts if component unmounts
    };
  }, [style]);

  return (
    <div
      ref={elementRef}
      className={`absolute ${className} floating-element`}
      style={{
        '--depth': depth,
        animationDelay: `${depth * 0.2}s`,
        ...style
      } as React.CSSProperties}
    >
      {children}
    </div>
  )
}

export default Floating