[tool.poetry]
name = "emma-studio"
version = "0.1.0"
description = "Backend for Emma Studio"
authors = ["Emma Studio Team <<EMAIL>>"]
readme = "README.md"
packages = [{include = "app"}]

[tool.poetry.dependencies]
python = ">=3.10,<3.13"  # Specify Python version compatibility
setuptools = "^80.8.0"
fastapi = "^0.115.0"  # Updated version compatible with Google ADK and Pydantic 2.x
uvicorn = {extras = ["standard"], version = "^0.34.0"}
pydantic = "^2.0.0"  # Updated to version 2.x for compatibility
python-dotenv = "^1.0.0"
requests = "^2.31.0"
openai = "^1.0.0"
redis = "^5.0.1"
google-generativeai = "^0.3.0"
loguru = "^0.7.3"
sentry-sdk = {extras = ["fastapi"], version = "^2.27.0"}
sqlalchemy = "^2.0.0"
alembic = "^1.15.0"
httpx = "^0.28.1"
typing-extensions = "^4.10.0"
starlette = "^0.46.2"  # Compatible with Google ADK and FastAPI
python-multipart = "^0.0.9"
pillow = "^10.0.0"  # For image processing in video generation
supabase = "^2.0.0"  # Supabase client for database operations

# Note: Emma Studio uses a custom agent implementation, not CrewAI
# The following dependencies are for AI services and utilities
psycopg2-binary = "^2.9.10"
librosa = "^0.11.0"
nltk = "^3.9.1"
transformers = "^4.52.3"
torch = "^2.7.0"
selenium = "^4.33.0"
chromedriver-autoinstaller = "^0.6.4"
beautifulsoup4 = "^4.13.4"
fake-useragent = "^2.2.0"
selenium-stealth = "^1.0.6"
undetected-chromedriver = "^3.5.5"
markdownify = "^1.1.0"
soundfile = "^0.13.1"
colorama = "^0.4.6"
termcolor = "^3.1.0"
# ollama = "<0.4.0"  # Temporarily disabled due to httpx version conflicts
webdriver-manager = "^4.0.2"
aiofiles = "^24.1.0"
celery = "^5.5.2"
pypdf2 = "^3.0.1"
reportlab = "^4.4.1"
aiohttp = "^3.12.7"
# Google ADK dependencies for Marketing Agency integration
# google-adk = "^1.0.0"  # Temporarily disabled due to dependency conflicts
# google-genai = "^1.9.0"  # Temporarily disabled due to dependency conflicts

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.3"
black = "^23.11.0"
flake8 = "^6.1.0"
mypy = "^1.7.1"
pre-commit = "^4.2.0"
isort = "^5.12.0"
pytest-asyncio = "^0.23.5"
pytest-cov = "^4.1.0"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

# Add tool configurations for better development experience
[tool.black]
line-length = 88
target-version = ["py310", "py311", "py312"]
include = '\.pyi?$'

[tool.isort]
profile = "black"
line_length = 88
multi_line_output = 3

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false
disallow_incomplete_defs = false