import React, { useState, useCallback } from "react";
import { useLocation } from "wouter";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { useToast } from "@/hooks/use-toast";
import { productPlacementService, SceneSuggestion, Style } from "@/services/product-placement-service";

// Import refactored components
import { HeaderSection } from "@/components/product-placement/header-section";
import { ProductUploader } from "@/components/product-placement/product-uploader";
import { BackgroundGenerator } from "@/components/product-placement/background-generator";
import { BackgroundOptions } from "@/components/product-placement/background-options";
import { PositioningControls } from "@/components/product-placement/positioning-controls";
import { PreviewPanel } from "@/components/product-placement/preview-panel";

interface BackgroundOption {
  url: string;
  data: string;
  seed: number;
  prompt: string;
  is_image_safe: boolean;
  style_type: string;
}

interface ProductPlacementState {
  productImage: string | null;
  backgroundImage: string | null;
  backgroundOptions: BackgroundOption[];
  selectedBackgroundIndex: number;
  compositeImage: string | null;
  position: { x: number; y: number };
  scale: number;
  rotation: number;
  shadowIntensity: number;
  isGeneratingBackground: boolean;
  isCompositing: boolean;
  isProcessingProduct: boolean;
}

export default function ProductPlacementPage() {
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  
  const [state, setState] = useState<ProductPlacementState>({
    productImage: null,
    backgroundImage: null,
    backgroundOptions: [],
    selectedBackgroundIndex: 0,
    compositeImage: null,
    position: { x: 0.5, y: 0.5 },
    scale: 1.0,
    rotation: 0,
    shadowIntensity: 0.3,
    isGeneratingBackground: false,
    isCompositing: false,
    isProcessingProduct: false,
  });

  const [sceneSuggestions, setSceneSuggestions] = useState<SceneSuggestion[]>([]);
  const [styles, setStyles] = useState<Style[]>([]);
  const [selectedStyle, setSelectedStyle] = useState("realistic");

  // Load scene suggestions and styles on component mount
  React.useEffect(() => {
    loadSceneSuggestions();
    loadStyles();
  }, []);

  const loadSceneSuggestions = async () => {
    try {
      const result = await productPlacementService.getSceneSuggestions();
      if (result.success && result.suggestions) {
        setSceneSuggestions(result.suggestions);
      }
    } catch (error) {
      console.error("Error loading scene suggestions:", error);
    }
  };

  const loadStyles = async () => {
    try {
      const result = await productPlacementService.getStyles();
      if (result.success && result.styles) {
        setStyles(result.styles);
      }
    } catch (error) {
      console.error("Error loading styles:", error);
    }
  };

  const handleProductUpload = useCallback(async (file: File) => {
    // Validate file first
    const validation = productPlacementService.validateImageFile(file);
    if (!validation.valid) {
      toast({
        title: "❌ Archivo inválido",
        description: validation.error,
        variant: "destructive",
      });
      return;
    }

    setState(prev => ({ ...prev, isProcessingProduct: true }));
    
    try {
      const result = await productPlacementService.uploadProduct(file);
      
      if (result.success && result.image_data) {
        setState(prev => ({ 
          ...prev, 
          productImage: result.image_data || null,
          isProcessingProduct: false 
        }));
        toast({
          title: "✅ Producto cargado",
          description: "La imagen del producto se procesó correctamente",
        });
        
        // Auto-composite if background is already loaded
        if (state.backgroundImage) {
          setTimeout(() => compositeImages(), 1000);
        }
      } else {
        throw new Error(result.error || "Error procesando imagen");
      }
    } catch (error) {
      console.error("Error uploading product:", error);
      setState(prev => ({ ...prev, isProcessingProduct: false }));
      toast({
        title: "❌ Error",
        description: "No se pudo procesar la imagen del producto",
        variant: "destructive",
      });
    }
  }, [toast, state.backgroundImage]);

  const generateBackground = async (prompt: string) => {
    if (!prompt.trim()) {
      toast({
        title: "⚠️ Prompt vacío",
        description: "Por favor describe la escena que deseas generar",
        variant: "destructive",
      });
      return;
    }

    setState(prev => ({ ...prev, isGeneratingBackground: true }));
    
    try {
      const result = await productPlacementService.generateBackground({
        scene_prompt: prompt,
        style: selectedStyle,
        size: "1024x1024",
        product_image_data: state.productImage, // Send product as style reference
      });
      
      if (result.success && result.image_data) {
        console.log("🔍 Debug - Full result:", result);
        console.log("🔍 Debug - result.images:", result.images);
        console.log("🔍 Debug - result.images length:", result.images?.length);
        
        const backgroundOptions: BackgroundOption[] = result.images || [{ 
          url: result.image_url || "", 
          data: result.image_data || "",
          seed: result.metadata?.seed || 0,
          prompt: result.revised_prompt || "",
          is_image_safe: result.metadata?.is_image_safe || true,
          style_type: result.metadata?.style_type || "GENERAL"
        }];
        
        console.log("🔍 Debug - backgroundOptions:", backgroundOptions);
        console.log("🔍 Debug - backgroundOptions length:", backgroundOptions.length);
        
        setState(prev => ({ 
          ...prev, 
          backgroundImage: result.image_data || null,
          backgroundOptions: backgroundOptions,
          selectedBackgroundIndex: 0,
          isGeneratingBackground: false 
        }));
        
        const totalImages = backgroundOptions.length;
        toast({
          title: "🎨 Fondos generados",
          description: `Se generaron ${totalImages} opciones. Creando composición...`,
        });
        
        // Auto-composite with the first option if product is loaded
        if (state.productImage) {
          setTimeout(() => compositeImages(), 1500);
        }
      } else {
        throw new Error(result.error || "Error generando fondo");
      }
    } catch (error) {
      console.error("Error generating background:", error);
      setState(prev => ({ ...prev, isGeneratingBackground: false }));
      toast({
        title: "❌ Error",
        description: "No se pudo generar el fondo",
        variant: "destructive",
      });
    }
  };

  const compositeImages = async () => {
    if (!state.productImage || !state.backgroundImage) {
      toast({
        title: "⚠️ Faltan imágenes",
        description: "Necesitas cargar un producto y generar un fondo",
        variant: "destructive",
      });
      return;
    }

    setState(prev => ({ ...prev, isCompositing: true }));
    
    try {
      const result = await productPlacementService.compositeProduct({
        background_url: state.backgroundImage,
        product_image_data: state.productImage,
        position: state.position,
        scale: state.scale,
        rotation: state.rotation,
        shadow_intensity: state.shadowIntensity,
      });
      
      if (result.success && result.image_data) {
        setState(prev => ({ 
          ...prev, 
          compositeImage: result.image_data || null,
          isCompositing: false 
        }));
        toast({
          title: "🖼️ Composición creada",
          description: "El producto se colocó correctamente en el fondo",
        });
      } else {
        throw new Error(result.error || "Error creando composición");
      }
    } catch (error) {
      console.error("Error compositing images:", error);
      setState(prev => ({ ...prev, isCompositing: false }));
      toast({
        title: "❌ Error",
        description: "No se pudo crear la composición",
        variant: "destructive",
      });
    }
  };

  const selectBackgroundOption = (index: number) => {
    if (state.backgroundOptions[index]) {
      setState(prev => ({
        ...prev,
        selectedBackgroundIndex: index,
        backgroundImage: state.backgroundOptions[index].data,
        compositeImage: null // Reset composite when changing background
      }));
      
      toast({
        title: "🖼️ Fondo seleccionado",
        description: `Opción ${index + 1} seleccionada`,
      });
    }
  };

  const downloadComposite = () => {
    if (state.compositeImage) {
      productPlacementService.downloadImage(
        state.compositeImage,
        `product-placement-${Date.now()}.png`
      );
    }
  };

  return (
    <DashboardLayout pageTitle="Product Placement">
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20">
        {/* Header */}
        <HeaderSection
          productImage={state.productImage}
          backgroundImage={state.backgroundImage}
          compositeImage={state.compositeImage}
          onBack={() => setLocation("/dashboard/ads-central")}
        />

        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Panel - Controls */}
            <div className="lg:col-span-1 space-y-6">
              {/* Product Upload */}
              <ProductUploader
                productImage={state.productImage}
                isProcessingProduct={state.isProcessingProduct}
                onProductUpload={handleProductUpload}
              />

              {/* Background Generation */}
              <BackgroundGenerator
                sceneSuggestions={sceneSuggestions}
                styles={styles}
                selectedStyle={selectedStyle}
                isGeneratingBackground={state.isGeneratingBackground}
                onStyleChange={setSelectedStyle}
                onGenerateBackground={generateBackground}
              />

              {/* Background Options Selection */}
              <BackgroundOptions
                backgroundOptions={state.backgroundOptions}
                selectedBackgroundIndex={state.selectedBackgroundIndex}
                onSelectBackground={selectBackgroundOption}
              />

              {/* Positioning Controls */}
              {state.productImage && state.backgroundImage && (
                <PositioningControls
                  position={state.position}
                  scale={state.scale}
                  rotation={state.rotation}
                  shadowIntensity={state.shadowIntensity}
                  isCompositing={state.isCompositing}
                  onPositionChange={(position) => setState(prev => ({ ...prev, position }))}
                  onScaleChange={(scale) => setState(prev => ({ ...prev, scale }))}
                  onRotationChange={(rotation) => setState(prev => ({ ...prev, rotation }))}
                  onShadowIntensityChange={(shadowIntensity) => setState(prev => ({ ...prev, shadowIntensity }))}
                  onComposite={compositeImages}
                />
              )}
            </div>

            {/* Right Panel - Preview */}
            <div className="lg:col-span-2">
              <PreviewPanel
                compositeImage={state.compositeImage}
                backgroundImage={state.backgroundImage}
                onDownload={downloadComposite}
              />
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
